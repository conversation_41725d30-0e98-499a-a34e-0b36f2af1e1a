# Clinical Trial Research Report System Environment Variables

# GROQ API Configuration
GROQ_API_KEY=your_groq_api_key_here

# Application Settings
APP_TITLE=Clinical Trial Research Report System
APP_DESCRIPTION=Advanced RAG-based Clinical Trial Research Report Generator with all-MiniLM-L6-v2 (HF), Qdrant (HNSW), Few-shot Retrieval-Augmented, Paragraph Chunking

# Vector Database Settings (Local Qdrant)
QDRANT_COLLECTION_NAME=clinical_trials
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# LLM Settings
DEFAULT_MODEL=meta-llama/llama-4-scout-17b-16e-instruct
DEFAULT_TEMPERATURE=0.3
DEFAULT_MAX_TOKENS=1024

# Document Processing Settings
MAX_UPLOAD_SIZE_MB=10
MAX_FILES_PER_BATCH=10
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
