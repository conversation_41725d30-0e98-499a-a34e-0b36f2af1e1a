# 📋 Clinical Trial Research Report System

A comprehensive Clinical Trial Research Report generator using advanced RAG technology. This system processes clinical trial documents and generates detailed research reports using **all-MiniLM-L6-v2 (HF)** embeddings, **Qdrant (HNSW)** indexing, **Few-shot Retrieval-Augmented** generation, and **Paragraph** chunking strategies.

## Features

- **📄 Batch Document Upload**: Upload up to 10 PDF documents simultaneously
- **🔍 Cross-Document Search**: Query across all uploaded documents in a unified knowledge base
- **🧠 Advanced Embeddings**: Uses Hugging Face's `sentence-transformers/all-MiniLM-L6-v2`
- **🗄️ Unified Vector Storage**: Qdrant with HNSW indexing for efficient cross-document similarity search
- **🤖 AI-Powered Responses**: GROQ API with Llama-4-Scout-17B and few-shot prompting
- **💬 Interactive Interface**: Streamlit web application with real-time batch processing
- **📊 Source Attribution**: Clear document source tracking with similarity scores
- **🎯 Document Filtering**: Optional filtering by specific documents during search
- **📈 Analytics Dashboard**: Query statistics and performance monitoring

## Architecture

```
Multiple PDFs → Batch Processing → Text Extraction → Paragraph Chunking → Embedding Generation
                                                                                    ↓
                                                                        Unified Vector Storage (Qdrant)
                                                                                    ↓
User Query → Query Embedding → Cross-Document Similarity Search → Multi-Source Context Retrieval → LLM Response (GROQ)
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd RAG_project
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**:
   Create a `.env` file in the root directory:
   ```env
   GROQ_API_KEY=your_groq_api_key_here
   QDRANT_URL=http://localhost:6333
   QDRANT_API_KEY=your_qdrant_api_key_here  # Optional for local setup
   ```

4. **Qdrant Setup**:
   The application uses Qdrant's local in-memory storage by default.
   No additional setup or server installation required!

## Usage

1. **Start the application**:
   ```bash
   streamlit run app.py
   ```

2. **Upload your clinical trial PDFs**:
   - Upload up to 10 PDF files simultaneously using the multi-file uploader
   - Review uploaded files and validation status
   - Click "Process All Documents" to extract and index content from all files

3. **Query across all documents**:
   - Enter questions that will search across your entire knowledge base
   - Optionally filter by specific documents using advanced settings
   - View retrieved chunks with clear source attribution and similarity scores
   - Get AI-generated responses based on cross-document context

## Example Queries

- "What were the primary endpoints of this study?"
- "What was the patient population and inclusion criteria?"
- "What were the main safety findings?"
- "Describe the treatment protocol and dosing schedule"
- "What were the statistical methods used?"

## Technical Components

### Document Processing (`document_processor.py`)
- PDF text extraction using PyPDF2
- Text cleaning and normalization
- Paragraph-based chunking strategy
- Metadata preservation

### Embedding Generation (`embedding_generator.py`)
- Hugging Face sentence-transformers integration
- Batch embedding generation
- Similarity calculation utilities
- Caching for performance

### Vector Database (`vector_database.py`)
- Qdrant client integration
- HNSW indexing configuration
- Efficient similarity search
- Document management operations

### GROQ Integration (`groq_integration.py`)
- Llama-4-Scout-17B model access
- Few-shot prompting for clinical trials
- Streaming response support
- Context-aware response generation

### Streamlit Interface (`app.py`)
- Drag & drop file upload
- Real-time processing feedback
- Interactive query interface
- Results visualization
- Analytics dashboard with query statistics

## Configuration

### Advanced Settings
- **Top-K Retrieval**: Number of similar chunks to retrieve (1-10)
- **Temperature**: Response creativity level (0.0-1.0)
- **Max Tokens**: Maximum response length (256-2048)
- **Similarity Threshold**: Minimum similarity score for chunk retrieval

### Model Configuration
- **Embedding Model**: `sentence-transformers/all-MiniLM-L6-v2` (384 dimensions)
- **LLM Model**: `meta-llama/llama-4-scout-17b-16e-instruct` (Latest Llama 4 Scout)
- **Vector Database**: Qdrant with cosine similarity
- **Chunking Strategy**: Paragraph-based with size limits (100-1000 chars)

## API Keys Setup

### GROQ API Key
1. Visit [GROQ Console](https://console.groq.com/)
2. Create an account and generate an API key
3. Add to your `.env` file: `GROQ_API_KEY=your_key_here`

### Qdrant Setup
- The application uses Qdrant's local in-memory storage
- No external server or cloud setup required
- Data is stored in memory during the session

## Troubleshooting

### Common Issues

1. **GROQ API Key Error**:
   - Ensure your API key is correctly set in the `.env` file
   - Check API key validity and quota limits

2. **PDF Processing Error**:
   - Ensure the uploaded file is a valid PDF
   - Check if the PDF contains extractable text (not just images)

3. **Embedding Model Loading**:
   - First run may take time to download the model
   - Ensure stable internet connection

4. **Qdrant Storage**:
   - App uses local in-memory storage (no server required)
   - Data persists during the session but resets on restart

## Performance Optimization

- **Caching**: Models and embeddings are cached using Streamlit's caching
- **Batch Processing**: Embeddings are generated in batches for efficiency
- **Memory Management**: Large documents are processed in chunks
- **Vector Indexing**: HNSW indexing provides fast similarity search

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the documentation

---

**Note**: This application is designed for research and educational purposes. Ensure compliance with data privacy regulations when processing clinical trial documents.
