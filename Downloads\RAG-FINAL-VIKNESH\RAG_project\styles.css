/* Clinical Trial RAG Application - Professional Styling */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Modern Light & Dark Theme - Premium UI Design */
:root {
    /* Light Theme Colors */
    --light-primary: #6366f1;
    --light-secondary: #8b5cf6;
    --light-accent: #06b6d4;
    --light-accent-2: #10b981;
    --light-success: #059669;
    --light-warning: #d97706;
    --light-error: #dc2626;

    /* Light Backgrounds */
    --light-bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --light-bg-secondary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --light-bg-tertiary: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
    --light-surface: #ffffff;
    --light-surface-elevated: #f8fafc;
    --light-surface-hover: #f1f5f9;

    /* Light Glass Effects */
    --light-glass-bg: rgba(255, 255, 255, 0.8);
    --light-glass-border: rgba(255, 255, 255, 0.9);
    --light-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --light-glass-backdrop: blur(10px);

    /* Light Text Colors */
    --light-text-primary: #1e293b;
    --light-text-secondary: #475569;
    --light-text-muted: #64748b;
    --light-text-accent: #6366f1;

    /* Dark Theme Colors */
    --dark-primary: #6366f1;
    --dark-secondary: #8b5cf6;
    --dark-accent: #06b6d4;
    --dark-accent-2: #10b981;
    --dark-success: #10b981;
    --dark-warning: #f59e0b;
    --dark-error: #ef4444;

    /* Dark Backgrounds */
    --dark-bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --dark-bg-secondary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --dark-bg-tertiary: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
    --dark-surface: #1e293b;
    --dark-surface-elevated: #334155;
    --dark-surface-hover: #475569;

    /* Dark Glass Effects */
    --dark-glass-bg: rgba(30, 41, 59, 0.8);
    --dark-glass-border: rgba(51, 65, 85, 0.8);
    --dark-glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    --dark-glass-backdrop: blur(10px);

    /* Dark Text Colors */
    --dark-text-primary: #f8fafc;
    --dark-text-secondary: #cbd5e1;
    --dark-text-muted: #94a3b8;
    --dark-text-accent: #6366f1;

    /* Active Theme Variables (Default: Light) */
    --primary: var(--light-primary);
    --secondary: var(--light-secondary);
    --accent: var(--light-accent);
    --accent-2: var(--light-accent-2);
    --success: var(--light-success);
    --warning: var(--light-warning);
    --error: var(--light-error);

    --bg-primary: var(--light-bg-primary);
    --bg-secondary: var(--light-bg-secondary);
    --bg-tertiary: var(--light-bg-tertiary);
    --surface: var(--light-surface);
    --surface-elevated: var(--light-surface-elevated);
    --surface-hover: var(--light-surface-hover);

    --glass-bg: var(--light-glass-bg);
    --glass-border: var(--light-glass-border);
    --glass-shadow: var(--light-glass-shadow);
    --glass-backdrop: var(--light-glass-backdrop);

    --text-primary: var(--light-text-primary);
    --text-secondary: var(--light-text-secondary);
    --text-muted: var(--light-text-muted);
    --text-accent: var(--light-text-accent);

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-glow-accent: 0 0 20px rgba(6, 182, 212, 0.3);

    /* Layout */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
}

/* Dark Theme Override */
[data-theme="dark"] {
    --primary: var(--dark-primary);
    --secondary: var(--dark-secondary);
    --accent: var(--dark-accent);
    --accent-2: var(--dark-accent-2);
    --success: var(--dark-success);
    --warning: var(--dark-warning);
    --error: var(--dark-error);

    --bg-primary: var(--dark-bg-primary);
    --bg-secondary: var(--dark-bg-secondary);
    --bg-tertiary: var(--dark-bg-tertiary);
    --surface: var(--dark-surface);
    --surface-elevated: var(--dark-surface-elevated);
    --surface-hover: var(--dark-surface-hover);

    --glass-bg: var(--dark-glass-bg);
    --glass-border: var(--dark-glass-border);
    --glass-shadow: var(--dark-glass-shadow);
    --glass-backdrop: var(--dark-glass-backdrop);

    --text-primary: var(--dark-text-primary);
    --text-secondary: var(--dark-text-secondary);
    --text-muted: var(--dark-text-muted);
    --text-accent: var(--dark-text-accent);

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.4);
    --shadow-glow-accent: 0 0 20px rgba(6, 182, 212, 0.4);
}

/* Global Styles - Modern Light & Dark Theme */
.stApp, .app-container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    min-height: 100vh;
    transition: var(--transition);
}

/* Light Theme Background Overlay */
.stApp::before, .app-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    transition: var(--transition);
}

/* Dark Theme Background Overlay */
[data-theme="dark"] .stApp::before,
[data-theme="dark"].app-container::before {
    background:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
}

/* Completely remove all Streamlit default spacing and bars */
.stApp {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.stApp > header {
    display: none !important;
}

.stApp > div:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.main {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

.main .block-container {
    padding-top: 0 !important;
    margin-top: 0 !important;
    max-width: 100% !important;
}

/* Remove any top spacing from the first element */
.main > div:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Remove Streamlit's default toolbar/header bar */
.stToolbar {
    display: none !important;
}

/* Remove any default spacing from all containers */
div[data-testid="stAppViewContainer"] {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Remove top spacing from main content container */
div[data-testid="stAppViewContainer"] > .main {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Remove spacing from block container */
div[data-testid="block-container"] {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Additional Streamlit container overrides */
.element-container:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Remove any gap from the very first element */
.stMarkdown:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Ensure columns start from top */
div[data-testid="column"] {
    padding-top: 0 !important;
}

div[data-testid="column"]:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* More aggressive Streamlit overrides */
.stApp > div {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Target all possible Streamlit containers */
section[data-testid="stSidebar"] ~ div {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Remove default spacing from main content area */
section[data-testid="stSidebar"] ~ div > div {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Override any remaining top spacing */
* {
    margin-top: 0 !important;
}

/* Specifically target the main content wrapper */
.main > div {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* Remove gap from very first child elements */
body > div:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Nuclear option - remove all top spacing from everything */
.stApp, .stApp > div, .main, .main > div,
div[data-testid="stAppViewContainer"],
div[data-testid="block-container"],
.element-container, .stMarkdown {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Force the first column to have negative margin */
div[data-testid="column"]:first-child > div:first-child {
    margin-top: -2rem !important;
}

div[data-testid="stHeader"] {
    display: none !important;
}

div[data-testid="stToolbar"] {
    display: none !important;
}

/* Force remove any top margins/padding from all elements */
* {
    margin-top: 0 !important;
}

.main * {
    margin-top: 0 !important;
}

/* Main content area - Glassmorphism */
.main .block-container {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--glass-shadow) !important;
    padding: 0 2rem 2rem 2rem !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding-top: 0 !important;
}

/* Remove any extra spacing from Streamlit elements */
.main .block-container > div:first-child {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Sidebar styling - Modern Glass */
[data-testid="stSidebar"] {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border-right: 1px solid var(--glass-border) !important;
}

[data-testid="stSidebar"] .css-1d391kg {
    background: transparent !important;
}

/* Sidebar content styling */
[data-testid="stSidebar"] .stMarkdown {
    color: var(--text-primary) !important;
}

[data-testid="stSidebar"] h1,
[data-testid="stSidebar"] h2,
[data-testid="stSidebar"] h3 {
    color: var(--text-primary) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Hide unnecessary elements */
.stDeployButton {display: none;}

/* Remove extra spacing and borders */
.element-container {
    margin-bottom: 0.5rem !important;
}

/* Clean header area */
[data-testid="stHeader"] {
    background: transparent !important;
    border: none !important;
    height: auto !important;
}

/* Modern Glassmorphism Header */
.main-header-container {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--glass-shadow);
    padding: 2rem 0;
    margin: -2rem -2rem 2rem -2rem;
    margin-top: -2rem !important;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    top: 0;
}

.main-header-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-secondary);
    opacity: 0.1;
    z-index: -1;
    transition: var(--transition);
}

/* Theme Toggle Button */
.stButton > button[key="theme_toggle"] {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: 50px !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
    min-width: 80px !important;
    height: 40px !important;
}

.stButton > button[key="theme_toggle"]:hover {
    background: var(--surface-hover) !important;
    transform: scale(1.05) !important;
    box-shadow: var(--shadow-glow) !important;
    border-color: var(--primary) !important;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 2px solid var(--glass-border);
    border-radius: 50%;
    box-shadow: var(--shadow-glow);
    transition: var(--transition);
}

.logo-container:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow: var(--shadow-glow-blue);
}

.clinical-logo {
    font-size: 2.5rem;
    color: var(--text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    filter: drop-shadow(0 0 10px rgba(240, 147, 251, 0.5));
}

.header-text {
    text-align: center;
    color: var(--text-primary);
}

.main-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: -0.02em;
    background: var(--bg-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0.5rem 0 0 0;
    color: var(--text-secondary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tagline {
    font-size: 1rem;
    font-weight: 300;
    margin: 0.25rem 0 0 0;
    color: var(--text-muted);
    font-style: italic;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Modern Glass Cards */
.stContainer > div {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
    color: var(--text-primary) !important;
    position: relative;
    overflow: hidden;
}

.stContainer > div::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-secondary);
    opacity: 0.8;
}

.stContainer > div:hover {
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow: var(--shadow-glow) !important;
    border-color: rgba(240, 147, 251, 0.5) !important;
}

.stContainer > div:hover::before {
    height: 4px;
    box-shadow: 0 0 20px rgba(240, 147, 251, 0.6);
}

/* Upload Section Styling - Dark Theme */
.upload-section {
    background: var(--surface-dark) !important;
    padding: 2rem !important;
    border-radius: var(--border-radius) !important;
    box-shadow: var(--shadow-dark) !important;
    border: 2px dashed var(--border-primary) !important;
    transition: var(--transition) !important;
    margin-bottom: 2rem !important;
    color: var(--text-primary) !important;
}

.upload-section:hover {
    border-color: var(--border-accent) !important;
    box-shadow: var(--shadow-glow) !important;
    background: var(--surface-elevated) !important;
}

/* Button Styling */
.stButton > button {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    text-transform: none;
    letter-spacing: 0.01em;
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.stButton > button:active {
    transform: translateY(0);
}

/* Progress Bar Styling */
.stProgress > div > div > div {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: var(--border-radius);
    height: 8px;
}

/* Metric Styling */
.metric-container {
    background: var(--background-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
    text-align: center;
    transition: var(--transition);
}

.metric-container:hover {
    background: white;
    box-shadow: var(--shadow-light);
}

/* Sidebar Styling */
.css-1d391kg {
    background: white;
    border-right: 1px solid var(--border-light);
}

/* Expander Styling */
.streamlit-expanderHeader {
    background: var(--background-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.streamlit-expanderHeader:hover {
    background: white;
    box-shadow: var(--shadow-light);
}

/* Text Input Styling */
.stTextInput > div > div > input {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-light);
    transition: var(--transition);
    font-size: 1rem;
    padding: 0.75rem;
}

.stTextInput > div > div > input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(46, 134, 171, 0.1);
}

/* Success/Error Message Styling */
.stSuccess {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid var(--success-green);
    border-radius: var(--border-radius);
    color: var(--success-green);
}

.stError {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--error-red);
    border-radius: var(--border-radius);
    color: var(--error-red);
}

.stWarning {
    background: rgba(253, 126, 20, 0.1);
    border: 1px solid var(--warning-orange);
    border-radius: var(--border-radius);
    color: var(--warning-orange);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .logo-container {
        width: 60px;
        height: 60px;
    }
    
    .clinical-logo {
        font-size: 2rem;
    }
}

/* Suggested Prompts Styling */
.suggested-prompts-container {
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid var(--border-light);
}

/* Modern Glassmorphism Buttons */
.stButton > button {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-md) !important;
    padding: 0.75rem 1.5rem !important;
    margin: 0.25rem 0 !important;
    transition: var(--transition) !important;
    text-align: center !important;
    width: 100% !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    line-height: 1.4 !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
    position: relative;
    overflow: hidden;
}

.stButton > button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.stButton > button:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: var(--shadow-glow) !important;
    border-color: rgba(240, 147, 251, 0.5) !important;
    color: var(--text-accent) !important;
}

.stButton > button:hover::before {
    left: 100%;
}

.stButton > button:active {
    transform: translateY(0) scale(1) !important;
}

/* Primary Button - Gradient Magic */
.stButton > button[kind="primary"] {
    background: var(--bg-secondary) !important;
    border: 1px solid rgba(240, 147, 251, 0.3) !important;
    color: white !important;
    font-weight: 600 !important;
    box-shadow: var(--shadow-glow) !important;
}

.stButton > button[kind="primary"]:hover {
    background: var(--bg-tertiary) !important;
    box-shadow: var(--shadow-glow-blue) !important;
    transform: translateY(-3px) scale(1.05) !important;
}

/* Secondary Button */
.stButton > button[kind="secondary"] {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    color: var(--text-secondary) !important;
}

.stButton > button[kind="secondary"]:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    color: var(--text-primary) !important;
}

/* Modern Form Elements - Glassmorphism */
.stSelectbox > div > div {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-md) !important;
    font-weight: 500 !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
}

.stSelectbox > div > div:focus {
    border-color: rgba(240, 147, 251, 0.5) !important;
    box-shadow: var(--shadow-glow) !important;
}

/* Text Input - Glass Effect */
.stTextInput > div > div > input {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-md) !important;
    color: var(--text-primary) !important;
    padding: 0.75rem 1rem !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
}

.stTextInput > div > div > input:focus {
    border-color: rgba(240, 147, 251, 0.5) !important;
    box-shadow: var(--shadow-glow) !important;
    transform: scale(1.02) !important;
}

/* Text Area - Glass Effect */
.stTextArea > div > div > textarea {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-md) !important;
    color: var(--text-primary) !important;
    padding: 0.75rem 1rem !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
    resize: vertical !important;
}

.stTextArea > div > div > textarea:focus {
    border-color: rgba(240, 147, 251, 0.5) !important;
    box-shadow: var(--shadow-glow) !important;
    transform: scale(1.01) !important;
}

/* Slider - Modern Gradient */
.stSlider > div > div > div > div {
    background: var(--bg-secondary) !important;
    border-radius: var(--radius-sm) !important;
}

.stSlider > div > div > div > div > div {
    background: white !important;
    box-shadow: var(--shadow-glow) !important;
}

/* File Uploader - Glass Effect */
.stFileUploader > div {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 2px dashed var(--glass-border) !important;
    border-radius: var(--radius-lg) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
    position: relative;
    overflow: hidden;
}

.stFileUploader > div::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-secondary);
    opacity: 0.05;
    pointer-events: none;
}

.stFileUploader > div:hover {
    border-color: rgba(240, 147, 251, 0.5) !important;
    background: rgba(255, 255, 255, 0.15) !important;
    transform: scale(1.02) !important;
    box-shadow: var(--shadow-glow) !important;
}

/* Knowledge Base Status */
.knowledge-base-status {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin: 1rem 0;
    box-shadow: var(--shadow-light);
}

/* Enhanced File Upload Area */
.upload-area {
    border: 3px dashed var(--border-light);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--background-light);
    transition: var(--transition);
    margin: 1rem 0;
}

.upload-area:hover {
    border-color: var(--primary-blue);
    background: white;
}

.upload-area.dragover {
    border-color: var(--accent-teal);
    background: rgba(241, 143, 1, 0.1);
}

/* Processing Status */
.processing-status {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid var(--primary-blue);
    box-shadow: var(--shadow-light);
}

.processing-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

.processing-step.completed {
    color: var(--success-green);
}

.processing-step.active {
    color: var(--primary-blue);
    font-weight: 600;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Clean Layout Improvements - Dark Theme */
.stMarkdown {
    margin-bottom: 0.5rem !important;
    color: var(--text-primary) !important;
}

.stMarkdown h1, .stMarkdown h2, .stMarkdown h3 {
    margin-top: 1rem !important;
    margin-bottom: 0.5rem !important;
    color: var(--text-primary) !important;
}

/* Clean section dividers */
hr {
    border: none !important;
    height: 1px !important;
    background: var(--border-primary) !important;
    margin: 1rem 0 !important;
}

/* Modern Metrics - Glass Cards */
.stMetric {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-lg) !important;
    padding: 1.5rem !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
    position: relative;
    overflow: hidden;
}

.stMetric::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--bg-secondary);
}

.stMetric:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: var(--shadow-glow) !important;
}

.stMetric > div {
    color: var(--text-primary) !important;
    position: relative;
    z-index: 1;
}

/* Modern Info Boxes */
.stInfo, .stSuccess, .stWarning, .stError {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-lg) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
    position: relative;
    overflow: hidden;
}

.stInfo::before, .stSuccess::before, .stWarning::before, .stError::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
}

.stInfo::before {
    background: var(--bg-tertiary) !important;
}

.stSuccess::before {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%) !important;
}

.stWarning::before {
    background: linear-gradient(135deg, #ffe66d 0%, #ff9a56 100%) !important;
}

.stError::before {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
}

/* Modern Expanders */
.streamlit-expanderHeader {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-radius: var(--radius-md) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
    transition: var(--transition) !important;
}

.streamlit-expanderHeader:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: scale(1.01) !important;
}

.streamlit-expanderContent {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-backdrop) !important;
    border: 1px solid var(--glass-border) !important;
    border-top: none !important;
    border-radius: 0 0 var(--radius-md) var(--radius-md) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--glass-shadow) !important;
}

/* Remove extra containers and bars */
.block-container > div:first-child {
    padding-top: 0 !important;
}

/* Remove Streamlit's default toolbar/header bar */
[data-testid="stToolbar"] {
    display: none !important;
}

/* Clean main content area */
.main > div:first-child {
    padding-top: 0 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .main-title {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 0.9rem;
    }

    .main .block-container {
        padding: 1rem !important;
        margin-top: 0 !important;
    }
}

/* Modern Animations */
.fade-in {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating Animation for Cards */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.floating {
    animation: float 3s ease-in-out infinite;
}

/* Pulse Animation for Interactive Elements */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(240, 147, 251, 0.4);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(240, 147, 251, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Gradient Animation */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.gradient-animate {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--bg-tertiary);
}
