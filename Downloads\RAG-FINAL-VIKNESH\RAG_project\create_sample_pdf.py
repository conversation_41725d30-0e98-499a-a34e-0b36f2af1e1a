"""
Create a sample clinical trial PDF for testing real document evaluation
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocT<PERSON>plate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch

def create_sample_clinical_trial_pdf():
    """Create a sample clinical trial PDF document"""
    
    # Create PDF
    filename = "sample_clinical_trial.pdf"
    doc = SimpleDocTemplate(filename, pagesize=letter)
    
    # Get styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=16,
        spaceAfter=30,
        alignment=1  # Center alignment
    )
    
    # Content
    content = []
    
    # Title
    content.append(Paragraph("Phase III Randomized Controlled Clinical Trial", title_style))
    content.append(Paragraph("Efficacy and Safety of Novel Oncology Treatment", title_style))
    content.append(Spacer(1, 20))
    
    # Study Overview
    content.append(Paragraph("Study Overview", styles['Heading2']))
    content.append(Paragraph(
        "This is a multicenter, randomized, double-blind, placebo-controlled phase III clinical trial "
        "designed to evaluate the efficacy and safety of the investigational drug XYZ-123 in patients "
        "with advanced solid tumors.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Primary Endpoints
    content.append(Paragraph("Primary Endpoints", styles['Heading2']))
    content.append(Paragraph(
        "The primary endpoints of this study are overall survival (OS) and progression-free survival (PFS) "
        "measured at 12 months. Secondary endpoints include objective response rate, duration of response, "
        "and quality of life measures.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Sample Size
    content.append(Paragraph("Study Population and Sample Size", styles['Heading2']))
    content.append(Paragraph(
        "A total of 750 patients were enrolled across 25 clinical centers in North America and Europe. "
        "Patients were randomized in a 2:1 ratio to receive either XYZ-123 or placebo.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Inclusion Criteria
    content.append(Paragraph("Inclusion Criteria", styles['Heading2']))
    content.append(Paragraph(
        "Key inclusion criteria include: age ≥18 years, histologically confirmed advanced solid tumor, "
        "ECOG performance status 0-2, adequate organ function, and life expectancy >3 months. "
        "Patients must have measurable disease according to RECIST v1.1 criteria.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Exclusion Criteria
    content.append(Paragraph("Exclusion Criteria", styles['Heading2']))
    content.append(Paragraph(
        "Major exclusion criteria include: pregnancy or breastfeeding, severe cardiovascular disease, "
        "active infection, prior treatment with similar investigational agents, and concurrent use of "
        "strong CYP3A4 inhibitors or inducers.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Study Design
    content.append(Paragraph("Study Design and Methodology", styles['Heading2']))
    content.append(Paragraph(
        "This randomized, double-blind, placebo-controlled study follows a parallel-group design. "
        "Stratification factors include tumor type, prior therapy lines, and geographic region. "
        "The study duration is 24 months with a 12-month follow-up period.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Results
    content.append(Paragraph("Efficacy Results", styles['Heading2']))
    content.append(Paragraph(
        "The primary analysis demonstrated a statistically significant improvement in overall survival "
        "for patients receiving XYZ-123 compared to placebo (median OS: 18.2 vs 12.4 months, HR=0.68, "
        "95% CI: 0.55-0.84, p<0.001). Progression-free survival was also significantly improved "
        "(median PFS: 8.1 vs 4.2 months, HR=0.52, p<0.001).",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Safety
    content.append(Paragraph("Safety Profile", styles['Heading2']))
    content.append(Paragraph(
        "The most common adverse events (≥20% incidence) in the XYZ-123 group were fatigue (68%), "
        "nausea (45%), diarrhea (38%), decreased appetite (32%), and rash (28%). Grade 3-4 adverse "
        "events occurred in 42% of patients in the treatment group versus 28% in the placebo group.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Adverse Events
    content.append(Paragraph("Serious Adverse Events", styles['Heading2']))
    content.append(Paragraph(
        "Serious adverse events were reported in 35% of patients receiving XYZ-123 and 22% of patients "
        "receiving placebo. The most common serious adverse events were pneumonia (8%), febrile neutropenia (6%), "
        "and tumor hemorrhage (4%). Treatment-related deaths occurred in 2% of patients in the XYZ-123 group.",
        styles['Normal']
    ))
    content.append(Spacer(1, 12))
    
    # Conclusions
    content.append(Paragraph("Conclusions", styles['Heading2']))
    content.append(Paragraph(
        "XYZ-123 demonstrated significant clinical benefit with manageable toxicity in patients with "
        "advanced solid tumors. The favorable benefit-risk profile supports the use of XYZ-123 as a "
        "new treatment option for this patient population.",
        styles['Normal']
    ))
    
    # Build PDF
    doc.build(content)
    print(f"✅ Sample clinical trial PDF created: {filename}")
    return filename

if __name__ == "__main__":
    create_sample_clinical_trial_pdf()
