#!/usr/bin/env python3
"""
Test GROQ connection and LLM evaluation
"""

import os
import sys
from groq_integration import get_groq_client
from rag_evaluation import RAGEvaluator, EvaluationSample

def test_groq_connection():
    """Test basic GROQ connection"""
    print("🔍 Testing GROQ connection...")
    
    try:
        # Get GROQ client
        groq_client = get_groq_client()
        print(f"✅ GROQ client created successfully")
        print(f"📋 Model: {groq_client.model_name}")
        
        # Test basic API call
        client = groq_client.get_client()
        response = client.chat.completions.create(
            model="meta-llama/llama-4-scout-17b-16e-instruct",
            messages=[{"role": "user", "content": "Rate this answer on a scale of 0-1: The sky is blue. Answer: 0.9"}],
            max_tokens=50,
            temperature=0.1
        )
        
        content = response.choices[0].message.content.strip()
        print(f"✅ API call successful")
        print(f"📝 Response: '{content}'")
        
        return True
        
    except Exception as e:
        print(f"❌ GROQ connection failed: {e}")
        return False

def test_llm_evaluation():
    """Test LLM evaluation functions"""
    print("\n🧪 Testing LLM evaluation functions...")
    
    try:
        # Create evaluator
        groq_client = get_groq_client()
        evaluator = RAGEvaluator(groq_client=groq_client)
        
        # Test answer correctness
        print("🔍 Testing answer correctness...")
        correctness_score = evaluator.llm_answer_correctness(
            question="How many patients were in the study?",
            answer="The study included 100 patients.",
            ground_truth="100 patients were enrolled in the clinical trial."
        )
        print(f"✅ Correctness score: {correctness_score:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM evaluation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing GROQ Integration and LLM Evaluation")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ GROQ_API_KEY not found in environment variables")
        print("Please set your GROQ API key in the .env file")
        return
    
    print(f"✅ GROQ API key found (length: {len(api_key)})")
    
    # Test connection
    if not test_groq_connection():
        print("❌ Connection test failed, stopping...")
        return
    
    # Test evaluation
    if not test_llm_evaluation():
        print("❌ Evaluation test failed")
        return
    
    print("\n🎉 All tests passed! LLM evaluation should work now.")

if __name__ == "__main__":
    main()
