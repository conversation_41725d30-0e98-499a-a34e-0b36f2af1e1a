"""
Test script for real document-based evaluation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import streamlit as st
from real_document_evaluation import RealDocumentEvaluator
from document_processor import DocumentProcessor
import uuid
import time

def setup_mock_session_state():
    """Setup mock session state with sample document data"""
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    
    # Create mock document data
    mock_document_text = """
    Clinical Trial Protocol: Phase III Randomized Controlled Trial
    
    Primary Endpoints: Overall survival (OS) and progression-free survival (PFS) at 12 months.
    
    Sample Size: A total of 500 patients were enrolled in this multi-center randomized controlled trial.
    
    Inclusion Criteria: Patients must be 18 years or older with histologically confirmed diagnosis and ECOG performance status 0-2.
    
    Exclusion Criteria: Pregnancy, severe comorbidities, and prior treatment with similar agents.
    
    Study Design: This is a randomized, double-blind, placebo-controlled phase III clinical trial.
    
    Adverse Events: The most common adverse events included fatigue (45%), nausea (32%), and headache (28%).
    
    Efficacy Results: The primary endpoint of overall survival showed a significant improvement in the treatment group (HR=0.75, p<0.001).
    
    Safety Profile: The treatment was generally well-tolerated with manageable side effects.
    """
    
    # Create mock chunks
    chunks = [
        {
            'id': 0,
            'text': mock_document_text[:500],
            'length': 500,
            'embedding': [0.1] * 384  # Mock embedding
        },
        {
            'id': 1,
            'text': mock_document_text[500:1000],
            'length': 500,
            'embedding': [0.2] * 384  # Mock embedding
        },
        {
            'id': 2,
            'text': mock_document_text[1000:],
            'length': len(mock_document_text) - 1000,
            'embedding': [0.3] * 384  # Mock embedding
        }
    ]
    
    # Add to session state
    doc_id = str(uuid.uuid4())
    st.session_state.processed_documents[doc_id] = {
        'filename': 'test_clinical_trial.pdf',
        'chunks': chunks,
        'metadata': {
            'total_text_length': len(mock_document_text),
            'num_chunks': len(chunks),
            'processed_at': time.strftime("%Y-%m-%d %H:%M:%S")
        }
    }
    
    print(f"✅ Mock document added with ID: {doc_id}")
    print(f"📄 Document: test_clinical_trial.pdf")
    print(f"📊 Chunks: {len(chunks)}")

def test_question_generation():
    """Test question generation from documents"""
    print("\n🔍 Testing Question Generation")
    print("=" * 40)
    
    setup_mock_session_state()
    
    evaluator = RealDocumentEvaluator()
    
    # Test question generation
    questions = evaluator.generate_evaluation_questions(num_questions=5)
    
    print(f"✅ Generated {len(questions)} questions:")
    for i, q in enumerate(questions, 1):
        print(f"\nQ{i}: {q['question']}")
        print(f"Expected Answer: {q['ground_truth'][:100]}...")
        print(f"Pattern Type: {q['pattern_type']}")
        print(f"Source: {q['source_document']}")

def test_key_information_extraction():
    """Test key information extraction"""
    print("\n📋 Testing Key Information Extraction")
    print("=" * 40)
    
    setup_mock_session_state()
    
    evaluator = RealDocumentEvaluator()
    
    # Test information extraction
    key_info = evaluator.extract_key_information_from_documents()
    
    print(f"✅ Extracted {len(key_info)} pieces of key information:")
    for i, info in enumerate(key_info[:5], 1):  # Show first 5
        print(f"\n{i}. Pattern: {info['pattern_type']}")
        print(f"   Question: {info['question']}")
        print(f"   Answer: {info['ground_truth'][:80]}...")

if __name__ == "__main__":
    print("🧪 Testing Real Document-Based Evaluation")
    print("=" * 50)
    
    # Initialize Streamlit session state
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    
    try:
        test_key_information_extraction()
        test_question_generation()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
