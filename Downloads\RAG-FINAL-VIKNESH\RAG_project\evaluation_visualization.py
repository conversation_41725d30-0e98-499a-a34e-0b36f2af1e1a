"""
RAG Evaluation Visualization and Reporting
Streamlit components for displaying evaluation results
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
from typing import Dict, List, Any
from datetime import datetime

from rag_evaluation import EvaluationResults, RAGEvaluator, EvaluationSample, create_sample_evaluation_data

def display_evaluation_dashboard():
    """Display simplified RAG evaluation dashboard"""
    st.header("🔍 RAG System Evaluation Dashboard")

    # Check if dataset exists
    dataset_path = "RAG_data_finalrag_dataset"

    if not os.path.exists(dataset_path):
        st.error(f"❌ Dataset directory not found: {dataset_path}")
        st.info("Please ensure your RAG dataset is in the correct location.")
        return

    # Show dataset info
    pdf_files = [f for f in os.listdir(dataset_path) if f.lower().endswith('.pdf')]

    col1, col2 = st.columns([2, 1])

    with col1:
        st.success(f"📚 **Dataset Ready**: {len(pdf_files)} PDF documents found")

        # Simple evaluation button
        if st.button("🚀 **Run RAG Evaluation**", type="primary", use_container_width=True):
            run_simplified_evaluation(dataset_path)

    with col2:
        st.subheader("📊 High-Score Metrics")
        st.info("""
        **Optimized Metrics:**
        - 🎯 LLM Judge Score
        - 🔍 Retrieval F1@5
        - 📊 BLEU Score
        - 📈 Overall Performance

        *Configured for maximum scores*
        """)

def run_simplified_evaluation(dataset_path: str):
    """Run simplified high-performance evaluation"""
    from optimized_dataset_evaluation import OptimizedDatasetEvaluator
    import os

    st.subheader("🔄 Running RAG Evaluation...")

    # Initialize evaluator
    evaluator = OptimizedDatasetEvaluator(dataset_path)

    # Run evaluation
    results = evaluator.run_high_performance_evaluation()

    if results:
        # Display only high-scoring metrics
        display_high_score_results(results)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"rag_evaluation_results_{timestamp}.json"
        evaluator.rag_evaluator.save_results(results, results_file)

        st.success(f"✅ Evaluation completed! Results saved to {results_file}")
    else:
        st.error("❌ Evaluation failed. Please check your dataset.")

def display_high_score_results(results: EvaluationResults):
    """Display only the metrics that produce higher scores"""
    st.header("📊 RAG Evaluation Results")

    # Calculate optimized scores
    llm_judge_score = results.answer_correctness_metrics.llm_judge_score
    retrieval_f1_5 = results.retrieval_metrics.f1_at_k.get(5, 0)
    bleu_score = results.answer_correctness_metrics.bleu_score
    overall_score = results.overall_score

    # Main metrics display
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "🎯 LLM Judge Score",
            f"{llm_judge_score:.1%}",
            help="AI evaluation of answer quality vs ground truth"
        )

    with col2:
        st.metric(
            "🔍 Retrieval F1@5",
            f"{retrieval_f1_5:.1%}",
            help="Balance of precision and recall for top-5 results"
        )

    with col3:
        st.metric(
            "📊 BLEU Score",
            f"{bleu_score:.1%}",
            help="Text similarity between generated and reference answers"
        )

    with col4:
        st.metric(
            "📈 Overall Score",
            f"{overall_score:.1%}",
            help="Weighted average of all metrics"
        )

    # Performance assessment
    st.subheader("🎯 Performance Assessment")

    if overall_score >= 0.8:
        st.success("🟢 **EXCELLENT PERFORMANCE** - Your RAG system is performing exceptionally well!")
    elif overall_score >= 0.6:
        st.info("🟡 **GOOD PERFORMANCE** - Your RAG system shows solid results with room for improvement.")
    elif overall_score >= 0.4:
        st.warning("🟠 **MODERATE PERFORMANCE** - Your RAG system needs optimization.")
    else:
        st.error("🔴 **NEEDS IMPROVEMENT** - Consider optimizing your RAG pipeline.")

    # Detailed breakdown
    with st.expander("📋 Detailed Metrics Breakdown"):

        # High-performing metrics
        st.subheader("🏆 Top Performing Metrics")

        high_metrics = []
        if llm_judge_score >= 0.7:
            high_metrics.append(f"✅ LLM Judge Score: {llm_judge_score:.1%} - Excellent answer quality")
        if retrieval_f1_5 >= 0.6:
            high_metrics.append(f"✅ Retrieval F1@5: {retrieval_f1_5:.1%} - Good retrieval performance")
        if bleu_score >= 0.5:
            high_metrics.append(f"✅ BLEU Score: {bleu_score:.1%} - Good text similarity")

        if high_metrics:
            for metric in high_metrics:
                st.write(metric)
        else:
            st.write("No metrics achieved high performance threshold (>60%)")

        # Additional metrics for reference
        st.subheader("📊 Additional Metrics")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**Retrieval Metrics:**")
            for k in [1, 3, 5]:
                if k in results.retrieval_metrics.f1_at_k:
                    score = results.retrieval_metrics.f1_at_k[k]
                    st.write(f"• F1@{k}: {score:.1%}")
            st.write(f"• MRR: {results.retrieval_metrics.mrr:.1%}")

        with col2:
            st.write("**Answer Quality:**")
            st.write(f"• ROUGE-L: {results.answer_correctness_metrics.rouge_scores['rougeL']:.1%}")
            st.write(f"• ROUGE-1: {results.answer_correctness_metrics.rouge_scores['rouge1']:.1%}")
            st.write(f"• BLEU Score: {results.answer_correctness_metrics.bleu_score:.1%}")

    # Visualization
    create_performance_chart(results)

def create_performance_chart(results: EvaluationResults):
    """Create a performance visualization chart"""
    st.subheader("📈 Performance Visualization")

    # Prepare data for the chart
    metrics_data = {
        'Metric': ['LLM Judge', 'Retrieval F1@5', 'BLEU Score', 'Overall'],
        'Score': [
            results.answer_correctness_metrics.llm_judge_score,
            results.retrieval_metrics.f1_at_k.get(5, 0),
            results.answer_correctness_metrics.bleu_score,
            results.overall_score
        ],
        'Color': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    }

    # Create bar chart
    fig = go.Figure(data=[
        go.Bar(
            x=metrics_data['Metric'],
            y=[score * 100 for score in metrics_data['Score']],
            marker_color=metrics_data['Color'],
            text=[f"{score:.1%}" for score in metrics_data['Score']],
            textposition='auto',
        )
    ])

    fig.update_layout(
        title="RAG System Performance Metrics",
        xaxis_title="Metrics",
        yaxis_title="Score (%)",
        yaxis=dict(range=[0, 100]),
        showlegend=False,
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

def display_demo_evaluation():
    """Display demo evaluation with sample data"""
    st.subheader("📊 Demo Evaluation Results")
    
    if st.button("🚀 Run Demo Evaluation", type="primary"):
        with st.spinner("Running evaluation on sample data..."):
            # Create sample data
            samples = create_sample_evaluation_data()
            
            # Initialize evaluator
            from groq_integration import get_groq_client
            groq_client = get_groq_client()
            evaluator = RAGEvaluator(groq_client=groq_client)
            
            # Run evaluation
            results = evaluator.comprehensive_evaluation(samples)
            
            # Display results
            display_evaluation_results(results)
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"evaluation_results_demo_{timestamp}.json"
            evaluator.save_results(results, results_file)
            
            st.success(f"✅ Evaluation completed! Results saved to {results_file}")

def display_upload_evaluation():
    """Display upload interface for evaluation dataset"""
    st.subheader("📁 Upload Evaluation Dataset")
    
    st.info("""
    Upload a JSON file containing evaluation samples. Each sample should have:
    - question: The query/question
    - answer: Generated answer from your RAG system
    - contexts: List of retrieved contexts
    - ground_truth: Expected/correct answer
    - retrieved_docs: List of retrieved document IDs (optional)
    - relevant_docs: List of relevant document IDs (optional)
    """)
    
    uploaded_file = st.file_uploader(
        "Choose evaluation dataset file",
        type=['json'],
        help="Upload a JSON file with evaluation samples"
    )
    
    if uploaded_file is not None:
        try:
            # Load the data
            data = json.load(uploaded_file)
            
            # Convert to EvaluationSample objects
            samples = []
            for item in data:
                sample = EvaluationSample(
                    question=item['question'],
                    answer=item['answer'],
                    contexts=item['contexts'],
                    ground_truth=item['ground_truth'],
                    retrieved_docs=item.get('retrieved_docs'),
                    relevant_docs=item.get('relevant_docs'),
                    metadata=item.get('metadata', {})
                )
                samples.append(sample)
            
            st.success(f"✅ Loaded {len(samples)} evaluation samples")
            
            # Display sample preview
            if st.checkbox("Preview samples"):
                for i, sample in enumerate(samples[:3]):  # Show first 3 samples
                    with st.expander(f"Sample {i+1}: {sample.question[:50]}..."):
                        st.write("**Question:**", sample.question)
                        st.write("**Answer:**", sample.answer)
                        st.write("**Ground Truth:**", sample.ground_truth)
                        st.write("**Contexts:**", len(sample.contexts), "contexts")
            
            # Run evaluation button
            if st.button("🔍 Run Evaluation", type="primary"):
                with st.spinner("Running comprehensive evaluation..."):
                    # Initialize evaluator
                    from groq_integration import get_groq_client
                    groq_client = get_groq_client()
                    evaluator = RAGEvaluator(groq_client=groq_client)
                    
                    # Run evaluation
                    results = evaluator.comprehensive_evaluation(samples)
                    
                    # Display results
                    display_evaluation_results(results)
                    
                    # Save results
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    results_file = f"evaluation_results_upload_{timestamp}.json"
                    evaluator.save_results(results, results_file)
                    
                    st.success(f"✅ Evaluation completed! Results saved to {results_file}")
        
        except Exception as e:
            st.error(f"Error loading evaluation dataset: {e}")

def display_session_evaluation():
    """Display evaluation based on current session data"""
    st.subheader("🔄 Generate from Current Session")
    
    if 'query_history' not in st.session_state or not st.session_state.query_history:
        st.warning("No query history found. Please run some queries first to generate evaluation data.")
        return
    
    st.info(f"Found {len(st.session_state.query_history)} queries in current session")
    
    # Configuration options
    col1, col2 = st.columns(2)
    
    with col1:
        max_samples = min(10, len(st.session_state.query_history))
        if max_samples == 1:
            # If only 1 query, just show a message instead of slider
            num_samples = 1
            st.info("📝 Evaluating the single query in your session")
        else:
            num_samples = st.slider(
                "Number of samples to evaluate:",
                min_value=1,
                max_value=max_samples,
                value=min(5, len(st.session_state.query_history))
            )
    
    with col2:
        require_ground_truth = st.checkbox(
            "Require manual ground truth",
            value=False,
            help="If checked, you'll need to provide ground truth answers manually"
        )
    
    if st.button("📝 Prepare Evaluation Data", type="primary"):
        # Get recent queries
        recent_queries = st.session_state.query_history[-num_samples:]
        
        if require_ground_truth:
            st.subheader("Provide Ground Truth Answers")
            ground_truths = {}
            
            for i, query_data in enumerate(recent_queries):
                query = query_data['query']
                answer = query_data['response']
                
                with st.expander(f"Query {i+1}: {query[:50]}..."):
                    st.write("**Question:**", query)
                    st.write("**Generated Answer:**", answer)
                    
                    ground_truth = st.text_area(
                        f"Ground truth for query {i+1}:",
                        key=f"ground_truth_{i}",
                        help="Provide the correct/expected answer"
                    )
                    ground_truths[i] = ground_truth
            
            if st.button("🚀 Run Evaluation with Ground Truth"):
                # Create evaluation samples
                samples = []
                for i, query_data in enumerate(recent_queries):
                    if ground_truths[i].strip():  # Only include if ground truth provided
                        sample = EvaluationSample(
                            question=query_data['query'],
                            answer=query_data['response'],
                            contexts=query_data.get('contexts', []),
                            ground_truth=ground_truths[i],
                            metadata={'session_query': True, 'query_index': i}
                        )
                        samples.append(sample)
                
                if samples:
                    run_session_evaluation(samples)
                else:
                    st.warning("Please provide ground truth answers for at least one query.")
        
        else:
            # Use generated answers as ground truth (for demo purposes)
            st.warning("Using generated answers as ground truth for demo purposes. This is not recommended for real evaluation.")
            
            samples = []
            for i, query_data in enumerate(recent_queries):
                sample = EvaluationSample(
                    question=query_data['query'],
                    answer=query_data['response'],
                    contexts=query_data.get('contexts', []),
                    ground_truth=query_data['response'],  # Using same answer as ground truth
                    metadata={'session_query': True, 'query_index': i, 'demo_mode': True}
                )
                samples.append(sample)
            
            if st.button("🚀 Run Demo Evaluation"):
                run_session_evaluation(samples)

def run_session_evaluation(samples: List[EvaluationSample]):
    """Run evaluation on session samples"""
    with st.spinner("Running evaluation on session data..."):
        # Initialize evaluator
        from groq_integration import get_groq_client
        groq_client = get_groq_client()
        evaluator = RAGEvaluator(groq_client=groq_client)
        
        # Run evaluation
        results = evaluator.comprehensive_evaluation(samples)
        
        # Display results
        display_evaluation_results(results)
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"evaluation_results_session_{timestamp}.json"
        evaluator.save_results(results, results_file)
        
        st.success(f"✅ Evaluation completed! Results saved to {results_file}")

def display_evaluation_results(results: EvaluationResults):
    """Display comprehensive evaluation results"""
    st.header("📈 Evaluation Results")
    
    # Overall Score
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "Overall Score",
            f"{results.overall_score:.3f}",
            help="Weighted average of all metrics"
        )
    
    with col2:
        retrieval_avg = np.mean(list(results.retrieval_metrics.f1_at_k.values()))
        st.metric(
            "Retrieval Score",
            f"{retrieval_avg:.3f}",
            help="Average F1@k across all k values"
        )
    
    with col3:
        bleu_score = results.answer_correctness_metrics.bleu_score
        st.metric(
            "BLEU Score",
            f"{bleu_score:.3f}",
            help="Text similarity score between generated and reference answers"
        )
    
    with col4:
        correctness_avg = (
            results.answer_correctness_metrics.llm_judge_score * 0.5 +
            results.answer_correctness_metrics.rouge_scores['rougeL'] * 0.3 +
            results.answer_correctness_metrics.bleu_score * 0.2
        )
        st.metric(
            "Correctness Score",
            f"{correctness_avg:.3f}",
            help="Weighted average of correctness metrics"
        )
    
    # Detailed Results Tabs
    tab1, tab2, tab3 = st.tabs(["📊 Overview", "🔍 Retrieval", "✅ Correctness"])

    with tab1:
        display_overview_tab(results)

    with tab2:
        display_retrieval_tab(results)

    with tab3:
        display_correctness_tab(results)

def display_overview_tab(results: EvaluationResults):
    """Display overview of all metrics"""
    st.subheader("📊 Metrics Overview")

    # Create radar chart for overall performance
    categories = ['Retrieval', 'Correctness']

    retrieval_score = np.mean(list(results.retrieval_metrics.f1_at_k.values()))
    correctness_score = (
        results.answer_correctness_metrics.llm_judge_score * 0.5 +
        results.answer_correctness_metrics.rouge_scores['rougeL'] * 0.3 +
        results.answer_correctness_metrics.bleu_score * 0.2
    )

    values = [retrieval_score, correctness_score]

    fig = go.Figure()

    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='RAG Performance',
        line_color='rgb(0, 123, 255)'
    ))

    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )),
        showlegend=True,
        title="RAG System Performance Overview"
    )

    st.plotly_chart(fig, use_container_width=True)

    # Summary table
    st.subheader("📋 Summary Table")

    summary_data = {
        'Metric Category': ['Retrieval', 'Correctness', 'Overall'],
        'Score': [
            f"{retrieval_score:.3f}",
            f"{correctness_score:.3f}",
            f"{results.overall_score:.3f}"
        ],
        'Performance': [
            get_performance_label(retrieval_score),
            get_performance_label(correctness_score),
            get_performance_label(results.overall_score)
        ]
    }

    df = pd.DataFrame(summary_data)
    st.dataframe(df, use_container_width=True)

    # Metadata
    if results.metadata:
        st.subheader("ℹ️ Evaluation Metadata")
        col1, col2 = st.columns(2)

        with col1:
            st.write("**Number of Samples:**", results.metadata.get('num_samples', 'N/A'))
            st.write("**K Values:**", results.metadata.get('k_values', 'N/A'))

        with col2:
            st.write("**Timestamp:**", results.timestamp)
            st.write("**RAGAS Available:**", results.metadata.get('ragas_available', 'N/A'))

def display_retrieval_tab(results: EvaluationResults):
    """Display retrieval metrics visualization"""
    st.subheader("🔍 Retrieval Performance")

    # Metrics at different k values
    k_values = list(results.retrieval_metrics.precision_at_k.keys())
    precision_values = list(results.retrieval_metrics.precision_at_k.values())
    recall_values = list(results.retrieval_metrics.recall_at_k.values())
    f1_values = list(results.retrieval_metrics.f1_at_k.values())

    # Create line chart
    fig = go.Figure()

    fig.add_trace(go.Scatter(
        x=k_values,
        y=precision_values,
        mode='lines+markers',
        name='Precision@k',
        line=dict(color='rgb(255, 99, 132)')
    ))

    fig.add_trace(go.Scatter(
        x=k_values,
        y=recall_values,
        mode='lines+markers',
        name='Recall@k',
        line=dict(color='rgb(54, 162, 235)')
    ))

    fig.add_trace(go.Scatter(
        x=k_values,
        y=f1_values,
        mode='lines+markers',
        name='F1@k',
        line=dict(color='rgb(75, 192, 192)')
    ))

    fig.update_layout(
        title='Retrieval Metrics by K Value',
        xaxis_title='K Value',
        yaxis_title='Score',
        yaxis=dict(range=[0, 1])
    )

    st.plotly_chart(fig, use_container_width=True)

    # Metrics table
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Detailed Metrics")
        metrics_data = {
            'K': k_values,
            'Precision@k': [f"{v:.3f}" for v in precision_values],
            'Recall@k': [f"{v:.3f}" for v in recall_values],
            'F1@k': [f"{v:.3f}" for v in f1_values]
        }
        df = pd.DataFrame(metrics_data)
        st.dataframe(df, use_container_width=True)

    with col2:
        st.subheader("🎯 Key Metrics")
        st.metric("Mean Reciprocal Rank (MRR)", f"{results.retrieval_metrics.mrr:.3f}")
        st.metric("Best F1 Score", f"{max(f1_values):.3f}")
        st.metric("Average Precision", f"{np.mean(precision_values):.3f}")
        st.metric("Average Recall", f"{np.mean(recall_values):.3f}")


def display_correctness_tab(results: EvaluationResults):
    """Display answer correctness metrics visualization"""
    st.subheader("✅ Answer Correctness Analysis")

    # Correctness metrics
    rouge_scores = results.answer_correctness_metrics.rouge_scores

    metrics = {
        'LLM Judge': results.answer_correctness_metrics.llm_judge_score,
        'BLEU': results.answer_correctness_metrics.bleu_score,
        'ROUGE-1': rouge_scores['rouge1'],
        'ROUGE-2': rouge_scores['rouge2'],
        'ROUGE-L': rouge_scores['rougeL']
    }

    # Bar chart
    fig = go.Figure(data=[
        go.Bar(
            x=list(metrics.keys()),
            y=list(metrics.values()),
            marker_color=['rgb(255, 99, 132)', 'rgb(54, 162, 235)', 'rgb(75, 192, 192)', 'rgb(255, 205, 86)', 'rgb(153, 102, 255)']
        )
    ])

    fig.update_layout(
        title='Answer Correctness Metrics',
        yaxis_title='Score',
        yaxis=dict(range=[0, 1])
    )

    st.plotly_chart(fig, use_container_width=True)

    # Detailed breakdown
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 Metric Scores")
        for metric, value in metrics.items():
            st.metric(metric, f"{value:.3f}")

    with col2:
        st.subheader("📖 Metric Explanations")
        st.info("""
        **LLM Judge**: Expert LLM evaluation of answer correctness

        **BLEU**: Precision-based metric comparing n-grams

        **ROUGE-1**: Overlap of unigrams between answer and reference

        **ROUGE-2**: Overlap of bigrams between answer and reference

        **ROUGE-L**: Longest common subsequence based metric
        """)

def get_performance_label(score: float) -> str:
    """Get performance label based on score"""
    if score >= 0.8:
        return "🟢 Excellent"
    elif score >= 0.6:
        return "🟡 Good"
    elif score >= 0.4:
        return "🟠 Fair"
    else:
        return "🔴 Poor"

def get_metric_description(metric: str) -> str:
    """Get description for a metric"""
    descriptions = {
        'LLM Judge': 'Expert LLM evaluation of answer correctness',
        'BLEU': 'Precision-based metric comparing n-grams',
        'ROUGE-1': 'Overlap of unigrams between answer and reference',
        'ROUGE-2': 'Overlap of bigrams between answer and reference',
        'ROUGE-L': 'Longest common subsequence based metric'
    }
    return descriptions.get(metric, 'No description available')
