"""
Clinical Trial Research Report Generator
Generates comprehensive research reports from clinical trial documents using RAG
"""

import streamlit as st
from typing import List, Dict, Optional
from datetime import datetime
import json
from groq_integration import get_groq_client
from embedding_generator import get_embedding_generator
from vector_database import get_vector_database


class ClinicalReportGenerator:
    """Generates comprehensive clinical trial research reports"""
    
    def __init__(self):
        self.groq_client = get_groq_client()
        self.embedding_gen = get_embedding_generator()
        self.vector_db = get_vector_database()
    
    def generate_comprehensive_report(self, documents: Dict, report_type: str = "comprehensive") -> Dict:
        """
        Generate a comprehensive clinical trial research report
        
        Args:
            documents: Dictionary of processed documents
            report_type: Type of report to generate
            
        Returns:
            Dict: Generated report with sections
        """
        report_sections = {
            "executive_summary": self._generate_executive_summary(documents),
            "study_overview": self._generate_study_overview(documents),
            "methodology": self._generate_methodology_section(documents),
            "patient_population": self._generate_patient_population(documents),
            "efficacy_results": self._generate_efficacy_results(documents),
            "safety_profile": self._generate_safety_profile(documents),
            "statistical_analysis": self._generate_statistical_analysis(documents),
            "conclusions": self._generate_conclusions(documents),
            "recommendations": self._generate_recommendations(documents)
        }
        
        return {
            "title": "Clinical Trial Research Report",
            "generated_at": datetime.now().isoformat(),
            "document_count": len(documents),
            "report_type": report_type,
            "sections": report_sections
        }
    
    def _generate_executive_summary(self, documents: Dict) -> str:
        """Generate executive summary section"""
        query = "Provide an executive summary of the clinical trials including key findings, primary endpoints, and overall conclusions"
        return self._query_documents(query, documents, max_tokens=800)
    
    def _generate_study_overview(self, documents: Dict) -> str:
        """Generate study overview section"""
        query = "Describe the study design, objectives, and overall methodology of the clinical trials"
        return self._query_documents(query, documents, max_tokens=600)
    
    def _generate_methodology_section(self, documents: Dict) -> str:
        """Generate methodology section"""
        query = "Detail the study methodology including randomization, blinding, inclusion/exclusion criteria, and study procedures"
        return self._query_documents(query, documents, max_tokens=700)
    
    def _generate_patient_population(self, documents: Dict) -> str:
        """Generate patient population section"""
        query = "Describe the patient population including demographics, baseline characteristics, and enrollment numbers"
        return self._query_documents(query, documents, max_tokens=500)
    
    def _generate_efficacy_results(self, documents: Dict) -> str:
        """Generate efficacy results section"""
        query = "Summarize the efficacy results including primary and secondary endpoints, statistical significance, and clinical outcomes"
        return self._query_documents(query, documents, max_tokens=800)
    
    def _generate_safety_profile(self, documents: Dict) -> str:
        """Generate safety profile section"""
        query = "Detail the safety profile including adverse events, serious adverse events, discontinuation rates, and safety conclusions"
        return self._query_documents(query, documents, max_tokens=600)
    
    def _generate_statistical_analysis(self, documents: Dict) -> str:
        """Generate statistical analysis section"""
        query = "Describe the statistical methods, analysis populations, and key statistical results including p-values and confidence intervals"
        return self._query_documents(query, documents, max_tokens=500)
    
    def _generate_conclusions(self, documents: Dict) -> str:
        """Generate conclusions section"""
        query = "Provide the main conclusions and clinical implications of the study results"
        return self._query_documents(query, documents, max_tokens=400)
    
    def _generate_recommendations(self, documents: Dict) -> str:
        """Generate recommendations section"""
        query = "What are the clinical recommendations and future research directions based on these study results?"
        return self._query_documents(query, documents, max_tokens=400)
    
    def _query_documents(self, query: str, documents: Dict, max_tokens: int = 500) -> str:
        """
        Query the document collection and generate response
        
        Args:
            query: Query string
            documents: Document collection
            max_tokens: Maximum tokens for response
            
        Returns:
            str: Generated response
        """
        try:
            # Generate query embedding
            query_embedding = self.embedding_gen.generate_query_embedding(query)
            
            # Search for relevant chunks across all documents
            similar_chunks = self.vector_db.search_similar_chunks(
                query_embedding.tolist(),
                top_k=8,  # Get more chunks for comprehensive reports
                score_threshold=0.3
            )
            
            if not similar_chunks:
                return "No relevant information found in the documents for this section."
            
            # Generate response using GROQ with clinical report context
            response = self.groq_client.generate_response(
                query,
                similar_chunks,
                temperature=0.1,  # Low temperature for factual reporting
                max_tokens=max_tokens
            )
            
            return response
            
        except Exception as e:
            return f"Error generating section: {str(e)}"
    
    def format_report_for_display(self, report: Dict) -> str:
        """
        Format the report for display in Streamlit
        
        Args:
            report: Generated report dictionary
            
        Returns:
            str: Formatted report as markdown
        """
        formatted_report = f"""
# {report['title']}

**Generated:** {report['generated_at']}  
**Documents Analyzed:** {report['document_count']}  
**Report Type:** {report['report_type'].title()}

---

## Executive Summary
{report['sections']['executive_summary']}

---

## Study Overview
{report['sections']['study_overview']}

---

## Methodology
{report['sections']['methodology']}

---

## Patient Population
{report['sections']['patient_population']}

---

## Efficacy Results
{report['sections']['efficacy_results']}

---

## Safety Profile
{report['sections']['safety_profile']}

---

## Statistical Analysis
{report['sections']['statistical_analysis']}

---

## Conclusions
{report['sections']['conclusions']}

---

## Recommendations
{report['sections']['recommendations']}

---

*This report was generated using advanced RAG technology with all-MiniLM-L6-v2 embeddings, Qdrant HNSW indexing, and few-shot retrieval-augmented generation.*
"""
        return formatted_report
    
    def export_report_to_json(self, report: Dict, filename: str = None) -> str:
        """
        Export report to JSON format
        
        Args:
            report: Generated report dictionary
            filename: Optional filename
            
        Returns:
            str: JSON string of the report
        """
        if filename is None:
            filename = f"clinical_trial_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        return json.dumps(report, indent=2, ensure_ascii=False)


def display_report_generator():
    """Display the clinical trial research report generator interface"""
    st.subheader("📋 Clinical Trial Research Report Generator")
    st.markdown("Generate comprehensive research reports from your clinical trial documents using advanced RAG technology.")
    
    # Check if documents are available
    if not st.session_state.processed_documents:
        st.warning("⚠️ Please upload and process clinical trial documents first.")
        return
    
    # Report generation options
    col1, col2 = st.columns([2, 1])
    
    with col1:
        report_type = st.selectbox(
            "Select Report Type:",
            ["comprehensive", "executive_summary", "safety_focused", "efficacy_focused"],
            help="Choose the type of report to generate"
        )
    
    with col2:
        if st.button("🔬 Generate Report", type="primary"):
            with st.spinner("Generating comprehensive clinical trial research report..."):
                generator = ClinicalReportGenerator()
                report = generator.generate_comprehensive_report(
                    st.session_state.processed_documents,
                    report_type
                )
                
                # Store report in session state
                st.session_state.generated_report = report
                st.success("✅ Report generated successfully!")
    
    # Display generated report
    if hasattr(st.session_state, 'generated_report') and st.session_state.generated_report:
        st.markdown("---")
        
        # Report display options
        display_option = st.radio(
            "Display Format:",
            ["📄 Formatted Report", "📊 JSON Data", "💾 Export Options"],
            horizontal=True
        )
        
        generator = ClinicalReportGenerator()
        
        if display_option == "📄 Formatted Report":
            formatted_report = generator.format_report_for_display(st.session_state.generated_report)
            st.markdown(formatted_report)
            
        elif display_option == "📊 JSON Data":
            st.json(st.session_state.generated_report)
            
        elif display_option == "💾 Export Options":
            st.subheader("Export Options")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Download as JSON
                json_data = generator.export_report_to_json(st.session_state.generated_report)
                st.download_button(
                    label="📥 Download JSON",
                    data=json_data,
                    file_name=f"clinical_trial_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
            
            with col2:
                # Download as Markdown
                formatted_report = generator.format_report_for_display(st.session_state.generated_report)
                st.download_button(
                    label="📥 Download Markdown",
                    data=formatted_report,
                    file_name=f"clinical_trial_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    mime="text/markdown"
                )
