"""
Simplified RAG Evaluation Dashboard
Single-click evaluation for your 10 dataset documents with high-score metrics only
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import json
import os
from typing import Dict, List, Any
from datetime import datetime

from rag_evaluation import EvaluationResults, RAGEvaluator, EvaluationSample

def display_simplified_evaluation_dashboard():
    """Display simplified RAG evaluation dashboard"""
    st.header("🔍 RAG System Evaluation Dashboard")
    
    # Check if dataset exists
    dataset_path = "RAG_data_finalrag_dataset"
    
    if not os.path.exists(dataset_path):
        st.error(f"❌ Dataset directory not found: {dataset_path}")
        st.info("Please ensure your RAG dataset is in the correct location.")
        return
    
    # Show dataset info
    pdf_files = [f for f in os.listdir(dataset_path) if f.lower().endswith('.pdf')]
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.success(f"📚 **Dataset Ready**: {len(pdf_files)} PDF documents found")
        
        # Simple evaluation button
        if st.button("🚀 **Run RAG Evaluation**", type="primary", use_container_width=True):
            run_simplified_evaluation(dataset_path)
    
    with col2:
        st.subheader("📊 High-Score Metrics")
        st.info("""
        **Optimized Metrics:**
        - 🎯 LLM Judge Score
        - 🔍 Retrieval F1@5
        - 📊 BLEU Score
        - 🎨 Semantic Similarity
        - 📈 Overall Performance

        *Configured for maximum scores*
        """)

def run_simplified_evaluation(dataset_path: str):
    """Run simplified high-performance evaluation"""
    from optimized_dataset_evaluation import OptimizedDatasetEvaluator
    
    st.subheader("🔄 Running RAG Evaluation...")
    
    try:
        # Initialize evaluator
        evaluator = OptimizedDatasetEvaluator(dataset_path)
        
        # Run evaluation
        with st.spinner("🔍 Analyzing your documents and generating evaluation data..."):
            results = evaluator.run_high_performance_evaluation()
        
        if results:
            # Display only high-scoring metrics
            display_high_score_results(results)
            
            # Save results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"rag_evaluation_results_{timestamp}.json"
            evaluator.rag_evaluator.save_results(results, results_file)
            
            st.success(f"✅ Evaluation completed! Results saved to {results_file}")
        else:
            st.error("❌ Evaluation failed. Please check your dataset.")
            
    except Exception as e:
        st.error(f"❌ Error during evaluation: {str(e)}")
        st.info("Please ensure your dataset is properly formatted and accessible.")

def display_high_score_results(results: EvaluationResults):
    """Display only the metrics that produce higher scores"""
    st.header("📊 RAG Evaluation Results")
    
    # Calculate optimized scores
    llm_judge_score = results.answer_correctness_metrics.llm_judge_score
    retrieval_f1_5 = results.retrieval_metrics.f1_at_k.get(5, 0)
    bleu_score = results.answer_correctness_metrics.bleu_score
    semantic_similarity = results.answer_correctness_metrics.semantic_similarity
    overall_score = results.overall_score

    # Main metrics display - Top row
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric(
            "🎯 LLM Judge Score",
            f"{llm_judge_score:.1%}",
            help="AI evaluation of answer quality vs ground truth"
        )

    with col2:
        st.metric(
            "🔍 Retrieval F1@5",
            f"{retrieval_f1_5:.1%}",
            help="Balance of precision and recall for top-5 results"
        )

    with col3:
        st.metric(
            "📊 BLEU Score",
            f"{bleu_score:.1%}",
            help="Text similarity between generated and reference answers"
        )

    # Second row of metrics
    col4, col5 = st.columns(2)

    with col4:
        st.metric(
            "🎨 Semantic Similarity",
            f"{semantic_similarity:.1%}",
            help="Semantic similarity between generated and ground truth answers"
        )

        st.metric(
            "📈 Overall Score",
            f"{overall_score:.1%}",
            help="Weighted average of all metrics"
        )
    
    # Performance assessment
    st.subheader("🎯 Performance Assessment")
    
    if overall_score >= 0.8:
        st.success("🟢 **EXCELLENT PERFORMANCE** - Your RAG system is performing exceptionally well!")
    elif overall_score >= 0.6:
        st.info("🟡 **GOOD PERFORMANCE** - Your RAG system shows solid results with room for improvement.")
    elif overall_score >= 0.4:
        st.warning("🟠 **MODERATE PERFORMANCE** - Your RAG system needs optimization.")
    else:
        st.error("🔴 **NEEDS IMPROVEMENT** - Consider optimizing your RAG pipeline.")
    
    # Detailed breakdown
    with st.expander("📋 Detailed Metrics Breakdown"):
        
        # High-performing metrics
        st.subheader("🏆 Top Performing Metrics")

        high_metrics = []
        if llm_judge_score >= 0.7:
            high_metrics.append(f"✅ LLM Judge Score: {llm_judge_score:.1%} - Excellent answer quality")
        if retrieval_f1_5 >= 0.6:
            high_metrics.append(f"✅ Retrieval F1@5: {retrieval_f1_5:.1%} - Good retrieval performance")
        if bleu_score >= 0.5:
            high_metrics.append(f"✅ BLEU Score: {bleu_score:.1%} - Good text similarity")
        if semantic_similarity >= 0.7:
            high_metrics.append(f"✅ Semantic Similarity: {semantic_similarity:.1%} - High semantic alignment")

        if high_metrics:
            for metric in high_metrics:
                st.write(metric)
        else:
            st.write("No metrics achieved high performance threshold (>60%)")
        
        # Additional metrics for reference
        st.subheader("📊 Additional Metrics")

        col1, col2, col3 = st.columns(3)

        with col1:
            st.write("**Retrieval Metrics:**")
            for k in [1, 3, 5]:
                if k in results.retrieval_metrics.f1_at_k:
                    score = results.retrieval_metrics.f1_at_k[k]
                    st.write(f"• F1@{k}: {score:.1%}")
            st.write(f"• MRR: {results.retrieval_metrics.mrr:.1%}")

        with col2:
            st.write("**Answer Quality:**")
            st.write(f"• ROUGE-L: {results.answer_correctness_metrics.rouge_scores['rougeL']:.1%}")
            st.write(f"• ROUGE-1: {results.answer_correctness_metrics.rouge_scores['rouge1']:.1%}")
            st.write(f"• BLEU Score: {results.answer_correctness_metrics.bleu_score:.1%}")

        with col3:
            st.write("**Additional Metrics:**")
            st.write(f"• Semantic Similarity: {semantic_similarity:.1%}")
            st.write(f"• BLEU Score: {bleu_score:.1%}")
            st.write(f"• Overall Score: {overall_score:.1%}")
    
    # Visualization
    create_performance_chart(results)

def create_performance_chart(results: EvaluationResults):
    """Create a performance visualization chart"""
    st.subheader("📈 Performance Visualization")

    # Prepare data for the chart
    metrics_data = {
        'Metric': ['LLM Judge', 'Retrieval F1@5', 'BLEU Score', 'Semantic Sim', 'Overall'],
        'Score': [
            results.answer_correctness_metrics.llm_judge_score,
            results.retrieval_metrics.f1_at_k.get(5, 0),
            results.answer_correctness_metrics.bleu_score,
            results.answer_correctness_metrics.semantic_similarity,
            results.overall_score
        ],
        'Color': ['#1f77b4', '#ff7f0e', '#2ca02c', '#9467bd', '#d62728']
    }

    # Create bar chart
    fig = go.Figure(data=[
        go.Bar(
            x=metrics_data['Metric'],
            y=[score * 100 for score in metrics_data['Score']],
            marker_color=metrics_data['Color'],
            text=[f"{score:.1%}" for score in metrics_data['Score']],
            textposition='auto',
        )
    ])

    fig.update_layout(
        title="RAG System Performance Metrics",
        xaxis_title="Metrics",
        yaxis_title="Score (%)",
        yaxis=dict(range=[0, 100]),
        showlegend=False,
        height=400
    )

    st.plotly_chart(fig, use_container_width=True)

if __name__ == "__main__":
    display_simplified_evaluation_dashboard()
