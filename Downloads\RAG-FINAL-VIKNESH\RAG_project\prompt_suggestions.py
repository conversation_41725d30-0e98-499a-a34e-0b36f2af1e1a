"""
Smart Prompt Suggestions for Clinical Trial RAG System
Generates context-aware prompt suggestions based on uploaded documents
"""

import streamlit as st
import re
from typing import List, Dict, Set
from collections import Counter

class SmartPromptSuggester:
    """Generate intelligent prompt suggestions based on clinical trial content"""
    
    def __init__(self):
        # Base clinical trial prompt categories
        self.base_prompts = {
            "study_design": [
                "What was the study design and methodology?",
                "Describe the randomization and blinding procedures",
                "What were the inclusion and exclusion criteria?",
                "How long was the study duration and follow-up period?"
            ],
            "endpoints": [
                "What were the primary endpoints of this study?",
                "List all secondary endpoints and their results",
                "Were the primary endpoints met successfully?",
                "How were the endpoints measured and assessed?"
            ],
            "demographics": [
                "Describe the patient demographics and baseline characteristics",
                "What was the age range and gender distribution?",
                "How many patients were enrolled and completed the study?",
                "What were the baseline disease characteristics?"
            ],
            "safety": [
                "What adverse events were reported?",
                "Were there any serious adverse events or deaths?",
                "How did the safety profile compare between groups?",
                "What were the most common side effects?"
            ],
            "efficacy": [
                "What were the main efficacy results?",
                "How significant were the treatment effects?",
                "What was the response rate or success rate?",
                "How did the treatment compare to control/placebo?"
            ],
            "statistical": [
                "What statistical methods were used for analysis?",
                "What were the p-values and confidence intervals?",
                "Was the study adequately powered?",
                "How was missing data handled?"
            ]
        }
        
        # Keywords that trigger specific prompt categories
        self.keyword_triggers = {
            "study_design": ["randomized", "double-blind", "placebo", "controlled", "crossover", "parallel"],
            "endpoints": ["primary endpoint", "secondary endpoint", "outcome", "efficacy", "response"],
            "demographics": ["patients", "subjects", "age", "gender", "baseline", "characteristics"],
            "safety": ["adverse", "safety", "tolerability", "side effect", "death", "serious"],
            "efficacy": ["efficacy", "response", "improvement", "reduction", "benefit", "treatment"],
            "statistical": ["p-value", "confidence interval", "statistical", "significance", "power"]
        }
    
    def analyze_document_content(self, documents: Dict) -> Dict[str, int]:
        """Analyze document content to identify relevant topics"""
        topic_scores = {category: 0 for category in self.base_prompts.keys()}
        
        # Combine all document text
        all_text = ""
        for doc_data in documents.values():
            if 'chunks' in doc_data:
                for chunk in doc_data['chunks']:
                    all_text += chunk.get('text', '').lower() + " "
        
        # Score topics based on keyword frequency
        for category, keywords in self.keyword_triggers.items():
            for keyword in keywords:
                topic_scores[category] += len(re.findall(r'\b' + re.escape(keyword) + r'\b', all_text))
        
        return topic_scores
    
    def extract_specific_terms(self, documents: Dict) -> Set[str]:
        """Extract specific medical/clinical terms from documents"""
        specific_terms = set()
        
        # Common clinical trial terms to look for
        clinical_patterns = [
            r'\b[A-Z][a-z]+\s+\d+\s*mg\b',  # Drug dosages
            r'\b\d+\s*mg/kg\b',  # Dosage per kg
            r'\b\d+\s*weeks?\b',  # Time periods
            r'\b\d+\s*months?\b',  # Time periods
            r'\bPhase\s+[I-IV]+\b',  # Clinical phases
            r'\b[A-Z]{2,}\s*\d*\b',  # Abbreviations/codes
        ]
        
        all_text = ""
        for doc_data in documents.values():
            if 'chunks' in doc_data:
                for chunk in doc_data['chunks']:
                    all_text += chunk.get('text', '') + " "
        
        for pattern in clinical_patterns:
            matches = re.findall(pattern, all_text)
            specific_terms.update(matches)
        
        return specific_terms
    
    def generate_context_aware_prompts(self, documents: Dict) -> List[Dict[str, str]]:
        """Generate context-aware prompts based on document analysis"""
        if not documents:
            return []
        
        # Analyze document content
        topic_scores = self.analyze_document_content(documents)
        specific_terms = self.extract_specific_terms(documents)
        
        # Sort topics by relevance
        sorted_topics = sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)
        
        suggested_prompts = []
        
        # Add top relevant prompts from each category
        for category, score in sorted_topics[:4]:  # Top 4 categories
            if score > 0:  # Only include if relevant content found
                category_prompts = self.base_prompts[category]
                # Add top 2 prompts from this category
                for prompt in category_prompts[:2]:
                    suggested_prompts.append({
                        "text": prompt,
                        "category": category.replace("_", " ").title(),
                        "icon": self._get_category_icon(category)
                    })
        
        # Add cross-document comparison prompts if multiple documents
        if len(documents) > 1:
            comparison_prompts = [
                "Compare the primary endpoints across all studies",
                "What are the differences in patient populations between studies?",
                "How do the safety profiles compare across different trials?",
                "Summarize the key findings from all uploaded studies"
            ]
            
            for prompt in comparison_prompts[:2]:
                suggested_prompts.append({
                    "text": prompt,
                    "category": "Cross-Document Analysis",
                    "icon": "🔄"
                })
        
        # Add specific term-based prompts
        if specific_terms:
            term_list = list(specific_terms)[:3]  # Top 3 specific terms
            for term in term_list:
                suggested_prompts.append({
                    "text": f"Tell me more about {term} mentioned in the studies",
                    "category": "Specific Terms",
                    "icon": "🔍"
                })
        
        return suggested_prompts[:8]  # Limit to 8 suggestions
    
    def _get_category_icon(self, category: str) -> str:
        """Get appropriate icon for each category"""
        icons = {
            "study_design": "📋",
            "endpoints": "🎯",
            "demographics": "👥",
            "safety": "⚠️",
            "efficacy": "📈",
            "statistical": "📊"
        }
        return icons.get(category, "💡")
    
    def get_fallback_prompts(self) -> List[Dict[str, str]]:
        """Get fallback prompts when no documents are uploaded"""
        fallback = [
            {"text": "What were the primary endpoints of this study?", "category": "Endpoints", "icon": "🎯"},
            {"text": "Describe the patient demographics and baseline characteristics", "category": "Demographics", "icon": "👥"},
            {"text": "What adverse events were reported?", "category": "Safety", "icon": "⚠️"},
            {"text": "What were the main efficacy results?", "category": "Efficacy", "icon": "📈"},
            {"text": "What was the study design and methodology?", "category": "Study Design", "icon": "📋"},
            {"text": "How significant were the treatment effects?", "category": "Statistical", "icon": "📊"}
        ]
        return fallback


def display_suggested_prompts(documents: Dict):
    """Display suggested prompts and handle selection"""
    suggester = SmartPromptSuggester()

    if documents:
        prompts = suggester.generate_context_aware_prompts(documents)
        st.markdown("### 💡 Smart Prompt Suggestions")
        st.markdown("*Based on your uploaded documents*")
    else:
        prompts = suggester.get_fallback_prompts()
        st.markdown("### 💡 Example Prompts")
        st.markdown("*Upload documents to get personalized suggestions*")

    if not prompts:
        return

    # Create a grid layout for prompts
    cols = st.columns(2)

    for i, prompt in enumerate(prompts):
        col = cols[i % 2]
        with col:
            # Create a styled button for each prompt
            button_key = f"prompt_{i}_{hash(prompt['text'])}"
            button_text = f"{prompt['icon']} {prompt['text'][:50]}{'...' if len(prompt['text']) > 50 else ''}"

            if st.button(
                button_text,
                key=button_key,
                help=f"Category: {prompt['category']}\nClick to use this prompt",
                use_container_width=True
            ):
                # Update the query in session state
                st.session_state.current_query = prompt['text']
                # Force a rerun to update the text area
                st.rerun()
