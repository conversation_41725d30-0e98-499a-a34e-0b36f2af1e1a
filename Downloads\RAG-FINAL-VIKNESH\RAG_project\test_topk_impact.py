"""
Test the impact of different Top-K values on evaluation metrics
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import streamlit as st
from real_document_evaluation import RealDocumentEvaluator
from rag_evaluation import RAGEvaluator, EvaluationSample
import uuid
import time
import numpy as np

def setup_comprehensive_mock_data():
    """Setup comprehensive mock document data for testing"""
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    
    # Create comprehensive clinical trial document
    clinical_trial_text = """
    CLINICAL TRIAL PROTOCOL: Phase III Randomized Controlled Trial
    
    STUDY OVERVIEW:
    This is a multicenter, randomized, double-blind, placebo-controlled phase III clinical trial 
    designed to evaluate the efficacy and safety of XYZ-123 in patients with advanced solid tumors.
    
    PRIMARY ENDPOINTS:
    The primary endpoints of this study are overall survival (OS) and progression-free survival (PFS) 
    measured at 12 months. OS is defined as the time from randomization to death from any cause. 
    PFS is defined as the time from randomization to disease progression or death.
    
    SAMPLE SIZE AND POPULATION:
    A total of 750 patients were enrolled across 25 clinical centers in North America and Europe. 
    Patients were randomized in a 2:1 ratio to receive either XYZ-123 or placebo. The study 
    population included patients with various solid tumor types.
    
    INCLUSION CRITERIA:
    Key inclusion criteria include: age ≥18 years, histologically confirmed advanced solid tumor, 
    ECOG performance status 0-2, adequate organ function defined as normal liver and kidney function, 
    and life expectancy >3 months. Patients must have measurable disease according to RECIST v1.1 criteria.
    
    EXCLUSION CRITERIA:
    Major exclusion criteria include: pregnancy or breastfeeding, severe cardiovascular disease 
    including recent myocardial infarction, active infection requiring systemic therapy, 
    prior treatment with similar investigational agents, and concurrent use of strong CYP3A4 inhibitors.
    
    STUDY DESIGN AND METHODOLOGY:
    This randomized, double-blind, placebo-controlled study follows a parallel-group design. 
    Stratification factors include tumor type, number of prior therapy lines, and geographic region. 
    The study duration is 24 months with a 12-month follow-up period for survival analysis.
    
    EFFICACY RESULTS:
    The primary analysis demonstrated a statistically significant improvement in overall survival 
    for patients receiving XYZ-123 compared to placebo. Median OS was 18.2 months in the treatment 
    group versus 12.4 months in the placebo group (HR=0.68, 95% CI: 0.55-0.84, p<0.001). 
    Progression-free survival was also significantly improved with median PFS of 8.1 months 
    versus 4.2 months (HR=0.52, 95% CI: 0.41-0.66, p<0.001).
    
    SAFETY PROFILE AND ADVERSE EVENTS:
    The most common adverse events (≥20% incidence) in the XYZ-123 group were fatigue (68%), 
    nausea (45%), diarrhea (38%), decreased appetite (32%), and rash (28%). Grade 3-4 adverse 
    events occurred in 42% of patients in the treatment group versus 28% in the placebo group.
    
    SERIOUS ADVERSE EVENTS:
    Serious adverse events were reported in 35% of patients receiving XYZ-123 and 22% of patients 
    receiving placebo. The most common serious adverse events were pneumonia (8%), febrile neutropenia (6%), 
    and tumor hemorrhage (4%). Treatment-related deaths occurred in 2% of patients in the XYZ-123 group.
    
    STATISTICAL ANALYSIS:
    The primary efficacy analysis was conducted using the intention-to-treat population. 
    Overall survival was analyzed using the Kaplan-Meier method and compared using the log-rank test. 
    Hazard ratios were estimated using Cox proportional hazards regression models.
    
    CONCLUSIONS:
    XYZ-123 demonstrated significant clinical benefit with manageable toxicity in patients with 
    advanced solid tumors. The favorable benefit-risk profile supports the use of XYZ-123 as a 
    new treatment option for this patient population.
    """
    
    # Split into realistic chunks
    chunks = []
    sentences = clinical_trial_text.split('. ')
    current_chunk = ""
    chunk_id = 0
    
    for sentence in sentences:
        if len(current_chunk) + len(sentence) > 800:  # Max chunk size
            if current_chunk:
                chunks.append({
                    'id': chunk_id,
                    'text': current_chunk.strip(),
                    'length': len(current_chunk),
                    'embedding': np.random.rand(384).tolist()  # Mock embedding
                })
                chunk_id += 1
                current_chunk = sentence
            else:
                current_chunk = sentence
        else:
            current_chunk += ". " + sentence if current_chunk else sentence
    
    # Add final chunk
    if current_chunk:
        chunks.append({
            'id': chunk_id,
            'text': current_chunk.strip(),
            'length': len(current_chunk),
            'embedding': np.random.rand(384).tolist()
        })
    
    # Add to session state
    doc_id = str(uuid.uuid4())
    st.session_state.processed_documents[doc_id] = {
        'filename': 'comprehensive_clinical_trial.pdf',
        'chunks': chunks,
        'metadata': {
            'total_text_length': len(clinical_trial_text),
            'num_chunks': len(chunks),
            'processed_at': time.strftime("%Y-%m-%d %H:%M:%S")
        }
    }
    
    print(f"✅ Comprehensive mock document created")
    print(f"📄 Document: comprehensive_clinical_trial.pdf")
    print(f"📊 Total chunks: {len(chunks)}")
    print(f"📝 Average chunk length: {np.mean([c['length'] for c in chunks]):.0f} characters")
    
    return doc_id

def test_different_topk_values():
    """Test evaluation with different Top-K values"""
    print("\n🧪 TESTING DIFFERENT TOP-K VALUES")
    print("=" * 50)
    
    # Setup mock data
    doc_id = setup_comprehensive_mock_data()
    
    # Initialize evaluator
    evaluator = RealDocumentEvaluator()
    
    # Generate questions
    print("\n📋 Generating evaluation questions...")
    questions_data = evaluator.generate_evaluation_questions(num_questions=5)
    print(f"✅ Generated {len(questions_data)} questions")
    
    # Test different Top-K values
    topk_values = [3, 5, 7, 10, 15]
    results = {}
    
    print("\n🔍 Testing different Top-K values:")
    print("-" * 40)
    
    for topk in topk_values:
        print(f"\n🎯 Testing Top-K = {topk}")
        print(f"   Retrieving {topk} most similar chunks for each question...")
        
        try:
            # Create evaluation samples with current Top-K
            samples = evaluator.create_evaluation_samples_from_documents(questions_data, topk)
            
            # Run evaluation
            evaluation_results = evaluator.rag_evaluator.comprehensive_evaluation(samples)
            
            # Store results
            results[topk] = {
                'retrieval_f1': np.mean(list(evaluation_results.retrieval_metrics.f1_at_k.values())),
                'retrieval_precision': np.mean(list(evaluation_results.retrieval_metrics.precision_at_k.values())),
                'retrieval_recall': np.mean(list(evaluation_results.retrieval_metrics.recall_at_k.values())),
                'mrr': evaluation_results.retrieval_metrics.mrr,
                'groundedness': evaluation_results.groundedness_metrics.llm_groundedness_score,
                'answer_correctness': evaluation_results.answer_correctness_metrics.llm_judge_score,
                'overall_score': evaluation_results.overall_score
            }
            
            print(f"   ✅ Completed - Overall Score: {evaluation_results.overall_score:.3f}")
            
        except Exception as e:
            print(f"   ❌ Error with Top-K={topk}: {e}")
            results[topk] = None
    
    return results

def display_topk_comparison(results):
    """Display comparison of Top-K results"""
    print("\n📊 TOP-K COMPARISON RESULTS")
    print("=" * 60)
    
    # Header
    print(f"{'Top-K':<6} {'Overall':<8} {'Retrieval':<10} {'Precision':<10} {'Recall':<8} {'MRR':<6} {'Ground.':<8} {'Correct.':<8}")
    print(f"{'Value':<6} {'Score':<8} {'F1':<10} {'@K':<10} {'@K':<8} {'':<6} {'Score':<8} {'Score':<8}")
    print("-" * 60)
    
    best_overall = 0
    best_k = 0
    
    for topk in sorted(results.keys()):
        if results[topk] is None:
            print(f"{topk:<6} {'ERROR':<8} {'ERROR':<10} {'ERROR':<10} {'ERROR':<8} {'ERROR':<6} {'ERROR':<8} {'ERROR':<8}")
            continue
            
        r = results[topk]
        print(f"{topk:<6} {r['overall_score']:<8.3f} {r['retrieval_f1']:<10.3f} {r['retrieval_precision']:<10.3f} "
              f"{r['retrieval_recall']:<8.3f} {r['mrr']:<6.3f} {r['groundedness']:<8.3f} {r['answer_correctness']:<8.3f}")
        
        if r['overall_score'] > best_overall:
            best_overall = r['overall_score']
            best_k = topk
    
    print("-" * 60)
    print(f"🏆 BEST PERFORMING TOP-K: {best_k} (Overall Score: {best_overall:.3f})")
    
    # Analysis
    print(f"\n📈 ANALYSIS:")
    print(f"• Best Top-K value: {best_k}")
    print(f"• This suggests retrieving {best_k} chunks provides optimal balance")
    print(f"• Lower K values may miss relevant information")
    print(f"• Higher K values may introduce noise")

def provide_improvement_recommendations(results):
    """Provide specific recommendations for improvement"""
    print(f"\n🚀 RECOMMENDATIONS TO IMPROVE METRICS:")
    print("=" * 45)
    
    if not results:
        print("❌ No results available for analysis")
        return
    
    # Find best performing Top-K
    best_k = max(results.keys(), key=lambda k: results[k]['overall_score'] if results[k] else 0)
    best_result = results[best_k]
    
    print(f"1. 🎯 OPTIMAL TOP-K SETTING:")
    print(f"   • Use Top-K = {best_k} for best overall performance")
    print(f"   • This achieved {best_result['overall_score']:.1%} overall score")
    
    print(f"\n2. 📊 METRIC-SPECIFIC IMPROVEMENTS:")
    
    # Retrieval improvements
    if best_result['retrieval_f1'] < 0.6:
        print(f"   🔍 RETRIEVAL (Current F1: {best_result['retrieval_f1']:.1%}):")
        print(f"     • Improve document chunking strategy")
        print(f"     • Use domain-specific embeddings")
        print(f"     • Increase similarity threshold")
    
    # Groundedness improvements
    if best_result['groundedness'] < 0.7:
        print(f"   ⚖️ GROUNDEDNESS (Current: {best_result['groundedness']:.1%}):")
        print(f"     • Improve LLM prompts to focus on context")
        print(f"     • Add source citation requirements")
        print(f"     • Filter out low-quality chunks")
    
    # Answer correctness improvements
    if best_result['answer_correctness'] < 0.8:
        print(f"   ✅ ANSWER CORRECTNESS (Current: {best_result['answer_correctness']:.1%}):")
        print(f"     • Use larger/better LLM models")
        print(f"     • Improve few-shot prompting")
        print(f"     • Enhance ground truth extraction")
    
    print(f"\n3. 🎯 NEXT STEPS:")
    print(f"   • Test the recommended Top-K={best_k} in your real application")
    print(f"   • Upload actual clinical trial documents for more realistic evaluation")
    print(f"   • Monitor performance on different document types")

if __name__ == "__main__":
    print("🧪 TOP-K IMPACT ANALYSIS")
    print("=" * 30)
    
    # Initialize session state
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    
    try:
        # Test different Top-K values
        results = test_different_topk_values()
        
        # Display comparison
        display_topk_comparison(results)
        
        # Provide recommendations
        provide_improvement_recommendations(results)
        
        print(f"\n🎉 Analysis completed!")
        print(f"💡 Use these insights to optimize your RAG system performance!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
