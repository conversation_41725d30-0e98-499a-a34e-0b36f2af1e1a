"""
Real Document-Based RAG Evaluation System
Evaluates RAG performance using actual uploaded documents and real queries
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
import json
import time
from datetime import datetime
import re

from rag_evaluation import RAGEvaluator, EvaluationSample, EvaluationResults
from document_processor import DocumentProcessor
from embedding_generator import get_embedding_generator
from vector_database import get_vector_database
from groq_integration import get_groq_client

class RealDocumentEvaluator:
    """Evaluates RAG system using real uploaded documents"""
    
    def __init__(self):
        self.doc_processor = DocumentProcessor()
        self.embedding_gen = get_embedding_generator()
        self.vector_db = get_vector_database()
        self.groq_client = get_groq_client()
        self.rag_evaluator = RAGEvaluator(groq_client=self.groq_client)
    
    def extract_key_information_from_documents(self) -> List[Dict]:
        """Extract key information from uploaded documents to create evaluation questions"""
        if not st.session_state.processed_documents:
            return []
        
        key_info = []
        
        for doc_id, doc_data in st.session_state.processed_documents.items():
            filename = doc_data['filename']
            chunks = doc_data['chunks']
            
            # Extract potential questions from document chunks
            for chunk in chunks[:5]:  # Limit to first 5 chunks per document
                text = chunk['text']
                
                # Look for clinical trial specific information patterns
                questions = self._extract_questions_from_text(text, filename)
                key_info.extend(questions)
        
        return key_info
    
    def _extract_questions_from_text(self, text: str, filename: str) -> List[Dict]:
        """Extract potential evaluation questions from text"""
        questions = []
        
        # Clinical trial patterns to look for
        patterns = {
            "primary_endpoints": r"primary\s+endpoint[s]?\s*:?\s*([^.]+)",
            "sample_size": r"(?:enrolled|recruited|included)\s+(\d+)\s+(?:patients|subjects|participants)",
            "inclusion_criteria": r"inclusion\s+criteria\s*:?\s*([^.]+(?:\.[^.]*){0,2})",
            "exclusion_criteria": r"exclusion\s+criteria\s*:?\s*([^.]+(?:\.[^.]*){0,2})",
            "study_design": r"(?:randomized|controlled|double-blind|single-blind|open-label|phase\s+[I-IV]+)",
            "adverse_events": r"adverse\s+event[s]?\s*:?\s*([^.]+)",
            "efficacy": r"efficacy\s*:?\s*([^.]+)",
            "safety": r"safety\s*:?\s*([^.]+)"
        }
        
        for pattern_name, pattern in patterns.items():
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                if pattern_name == "primary_endpoints":
                    question = "What are the primary endpoints of this study?"
                    answer = match.group(1).strip()
                elif pattern_name == "sample_size":
                    question = "What is the sample size of this study?"
                    answer = f"The study enrolled {match.group(1)} participants."
                elif pattern_name == "inclusion_criteria":
                    question = "What are the inclusion criteria for this study?"
                    answer = match.group(1).strip()
                elif pattern_name == "exclusion_criteria":
                    question = "What are the exclusion criteria for this study?"
                    answer = match.group(1).strip()
                elif pattern_name == "study_design":
                    question = "What is the study design?"
                    answer = f"This is a {match.group(0).lower()} study."
                elif pattern_name == "adverse_events":
                    question = "What adverse events were reported?"
                    answer = match.group(1).strip()
                elif pattern_name == "efficacy":
                    question = "What were the efficacy results?"
                    answer = match.group(1).strip()
                elif pattern_name == "safety":
                    question = "What were the safety findings?"
                    answer = match.group(1).strip()
                
                if len(answer) > 20 and len(answer) < 500:  # Filter reasonable answers
                    questions.append({
                        'question': question,
                        'ground_truth': answer,
                        'source_document': filename,
                        'context_text': text,
                        'pattern_type': pattern_name
                    })
        
        return questions
    
    def generate_evaluation_questions(self, num_questions: int = 10) -> List[Dict]:
        """Generate evaluation questions from uploaded documents"""
        if not st.session_state.processed_documents:
            st.error("No documents uploaded. Please upload documents first.")
            return []
        
        # Extract key information
        key_info = self.extract_key_information_from_documents()
        
        if not key_info:
            # Fallback: Generate generic questions
            return self._generate_generic_questions(num_questions)
        
        # Select diverse questions
        selected_questions = []
        pattern_types_used = set()
        
        for info in key_info:
            if len(selected_questions) >= num_questions:
                break
            
            # Ensure diversity in question types
            if info['pattern_type'] not in pattern_types_used or len(selected_questions) < 3:
                selected_questions.append(info)
                pattern_types_used.add(info['pattern_type'])
        
        # Fill remaining slots with any available questions
        for info in key_info:
            if len(selected_questions) >= num_questions:
                break
            if info not in selected_questions:
                selected_questions.append(info)
        
        return selected_questions[:num_questions]
    
    def _generate_generic_questions(self, num_questions: int) -> List[Dict]:
        """Generate generic clinical trial questions when specific extraction fails"""
        generic_questions = [
            "What are the primary endpoints of this clinical trial?",
            "What is the sample size of this study?",
            "What are the inclusion criteria for participants?",
            "What are the exclusion criteria for participants?",
            "What is the study design and methodology?",
            "What adverse events were reported?",
            "What were the main efficacy results?",
            "What were the safety findings?",
            "What is the duration of the study?",
            "What statistical methods were used for analysis?"
        ]
        
        # Get first document for context
        first_doc = next(iter(st.session_state.processed_documents.values()))
        filename = first_doc['filename']
        context_text = first_doc['chunks'][0]['text'] if first_doc['chunks'] else ""
        
        questions = []
        for i, question in enumerate(generic_questions[:num_questions]):
            questions.append({
                'question': question,
                'ground_truth': "Please refer to the document for specific details.",
                'source_document': filename,
                'context_text': context_text,
                'pattern_type': 'generic'
            })
        
        return questions
    
    def run_real_rag_query(self, question: str, top_k: int = 5) -> Dict:
        """Run a real RAG query using the existing pipeline"""
        try:
            # Generate query embedding
            query_embedding = self.embedding_gen.generate_query_embedding(question)
            
            # Search for similar chunks
            similar_chunks = self.vector_db.search_similar_chunks(
                query_embedding.tolist(),
                top_k=top_k,
                score_threshold=0.1
            )
            
            # Generate response using GROQ
            response = self.groq_client.generate_response(
                question,
                similar_chunks,
                temperature=0.1,
                max_tokens=500
            )
            
            # Extract contexts and document IDs
            contexts = [chunk['text'] for chunk in similar_chunks]
            retrieved_docs = [f"{chunk['filename']}_{chunk['chunk_id']}" for chunk in similar_chunks]
            
            return {
                'question': question,
                'answer': response,
                'contexts': contexts,
                'retrieved_docs': retrieved_docs,
                'similarity_scores': [chunk['score'] for chunk in similar_chunks]
            }
            
        except Exception as e:
            st.error(f"Error running RAG query: {e}")
            return {
                'question': question,
                'answer': "Error generating response",
                'contexts': [],
                'retrieved_docs': [],
                'similarity_scores': []
            }
    
    def create_evaluation_samples_from_documents(self, questions_data: List[Dict], 
                                               top_k: int = 5) -> List[EvaluationSample]:
        """Create evaluation samples by running real RAG queries"""
        samples = []
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        for i, q_data in enumerate(questions_data):
            status_text.text(f"Processing question {i+1}/{len(questions_data)}: {q_data['question'][:50]}...")
            
            # Run real RAG query
            rag_result = self.run_real_rag_query(q_data['question'], top_k)
            
            # Determine relevant documents (documents that contain the ground truth)
            relevant_docs = []
            for doc_id, doc_data in st.session_state.processed_documents.items():
                if doc_data['filename'] == q_data['source_document']:
                    # Add chunk IDs from this document as relevant
                    for chunk in doc_data['chunks'][:3]:  # Consider first 3 chunks as potentially relevant
                        relevant_docs.append(f"{doc_data['filename']}_{chunk['id']}")
            
            # Create evaluation sample
            sample = EvaluationSample(
                question=q_data['question'],
                answer=rag_result['answer'],
                contexts=rag_result['contexts'],
                ground_truth=q_data['ground_truth'],
                retrieved_docs=rag_result['retrieved_docs'],
                relevant_docs=relevant_docs,
                metadata={
                    'source_document': q_data['source_document'],
                    'pattern_type': q_data['pattern_type'],
                    'similarity_scores': rag_result['similarity_scores'],
                    'evaluation_type': 'real_document_based'
                }
            )
            
            samples.append(sample)
            progress_bar.progress((i + 1) / len(questions_data))
        
        status_text.text("✅ All questions processed!")
        progress_bar.empty()
        status_text.empty()
        
        return samples
    
    def run_comprehensive_evaluation(self, num_questions: int = 10, 
                                   top_k: int = 5) -> EvaluationResults:
        """Run comprehensive evaluation using real documents"""
        st.info("🔍 Generating evaluation questions from your uploaded documents...")
        
        # Generate questions from documents
        questions_data = self.generate_evaluation_questions(num_questions)
        
        if not questions_data:
            st.error("Could not generate evaluation questions from documents.")
            return None
        
        st.success(f"✅ Generated {len(questions_data)} evaluation questions")
        
        # Show preview of questions
        with st.expander("📋 Preview Generated Questions"):
            for i, q_data in enumerate(questions_data[:3]):
                st.write(f"**Q{i+1}:** {q_data['question']}")
                st.write(f"**Expected Answer:** {q_data['ground_truth'][:100]}...")
                st.write(f"**Source:** {q_data['source_document']}")
                st.write("---")
        
        st.info("🤖 Running RAG queries and evaluating responses...")
        
        # Create evaluation samples
        samples = self.create_evaluation_samples_from_documents(questions_data, top_k)
        
        # Run evaluation
        results = self.rag_evaluator.comprehensive_evaluation(samples)
        
        return results


def display_real_document_evaluation():
    """Display real document-based evaluation interface"""
    st.header("📄 Real Document-Based RAG Evaluation")
    
    # Check if documents are uploaded
    if not st.session_state.processed_documents:
        st.warning("⚠️ No documents uploaded. Please upload documents first in the 'Document Upload' section.")
        return
    
    # Show document status
    total_docs = len(st.session_state.processed_documents)
    total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())
    
    st.success(f"📚 Ready to evaluate: {total_docs} documents with {total_chunks} total chunks")
    
    # Configuration options
    col1, col2 = st.columns(2)
    
    with col1:
        num_questions = st.slider(
            "Number of evaluation questions:",
            min_value=3,
            max_value=20,
            value=10,
            help="Number of questions to generate from your documents"
        )
    
    with col2:
        top_k = st.slider(
            "Top-K retrieval:",
            min_value=3,
            max_value=15,
            value=5,
            help="Number of document chunks to retrieve for each question"
        )
    
    # Run evaluation button
    if st.button("🚀 Run Real Document Evaluation", type="primary"):
        evaluator = RealDocumentEvaluator()
        
        with st.spinner("Running comprehensive evaluation on your documents..."):
            results = evaluator.run_comprehensive_evaluation(num_questions, top_k)
            
            if results:
                # Display results using existing visualization
                from evaluation_visualization import display_evaluation_results
                display_evaluation_results(results)
                
                # Save results
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                results_file = f"real_document_evaluation_{timestamp}.json"
                evaluator.rag_evaluator.save_results(results, results_file)
                
                st.success(f"✅ Real document evaluation completed! Results saved to {results_file}")
            else:
                st.error("❌ Evaluation failed. Please check your documents and try again.")
