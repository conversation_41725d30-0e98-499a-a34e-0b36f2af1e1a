"""
Test script to verify the improved metrics are working
"""

from rag_evaluation import RAGEvaluator, EvaluationSample
from groq_integration import get_groq_client

def test_metrics():
    """Test the improved metrics"""
    print("🧪 Testing improved RAG evaluation metrics...")
    
    # Initialize evaluator
    groq_client = get_groq_client()
    evaluator = RAGEvaluator(groq_client=groq_client)
    
    # Create test samples
    test_samples = [
        EvaluationSample(
            question="What is the primary endpoint of this clinical trial?",
            answer="The primary endpoint is overall survival measured at 24 months.",
            contexts=["The study's primary endpoint is overall survival at 24 months follow-up."],
            ground_truth="The primary endpoint is overall survival at 24 months.",
            retrieved_docs=["doc1"],
            relevant_docs=["doc1"]
        ),
        EvaluationSample(
            question="What are the inclusion criteria?",
            answer="Patients must be 18 years or older with confirmed diagnosis.",
            contexts=["Inclusion criteria: age ≥18 years, confirmed diagnosis of the condition."],
            ground_truth="Inclusion criteria include age 18+ and confirmed diagnosis.",
            retrieved_docs=["doc2"],
            relevant_docs=["doc2"]
        )
    ]
    
    print(f"📊 Testing with {len(test_samples)} samples...")
    
    # Test individual metrics
    print("\n🔍 Testing individual metrics:")
    
    sample = test_samples[0]
    
    # Test BLEU scores
    bleu_score = evaluator.calculate_bleu_score(sample.ground_truth, sample.answer)
    enhanced_bleu = evaluator.calculate_enhanced_bleu_score(sample.ground_truth, sample.answer)
    print(f"✅ BLEU Score (Original): {bleu_score:.3f}")
    print(f"✅ BLEU Score (Enhanced): {enhanced_bleu:.3f}")
    
    # Test semantic similarity
    semantic_score = evaluator.calculate_semantic_similarity(sample.ground_truth, sample.answer)
    print(f"✅ Semantic Similarity: {semantic_score:.3f}")
    
    # Test context precision
    context_precision = evaluator.calculate_context_precision(sample.contexts, sample.relevant_docs)
    print(f"✅ Context Precision: {context_precision:.3f}")
    
    # Test context precision (moved from groundedness)
    print(f"✅ Context Precision: {context_precision:.3f}")
    
    # Test comprehensive evaluation
    print("\n🚀 Running comprehensive evaluation...")
    results = evaluator.comprehensive_evaluation(test_samples)
    
    print("\n📈 Results Summary:")
    print(f"• LLM Judge Score: {results.answer_correctness_metrics.llm_judge_score:.1%}")
    print(f"• BLEU Score: {results.answer_correctness_metrics.bleu_score:.1%}")
    print(f"• Semantic Similarity: {results.answer_correctness_metrics.semantic_similarity:.1%}")
    print(f"• Context Precision: {context_precision:.1%}")

    # Show F1 scores
    print(f"\n🎯 F1 Scores:")
    for k in [1, 3, 5]:
        if k in results.retrieval_metrics.f1_at_k:
            f1_score = results.retrieval_metrics.f1_at_k[k]
            print(f"• F1@{k}: {f1_score:.1%}")

    print(f"\n• Overall Score: {results.overall_score:.1%}")
    
    print("\n✅ All metrics are working correctly!")
    return results

if __name__ == "__main__":
    test_metrics()
