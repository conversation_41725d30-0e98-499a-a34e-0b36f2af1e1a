"""
Test script for RAG evaluation framework
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag_evaluation import RAGEvaluator, create_sample_evaluation_data
from groq_integration import get_groq_client

def test_evaluation():
    """Test the evaluation framework"""
    print("🔍 Testing RAG Evaluation Framework")
    print("=" * 50)
    
    # Create sample data
    print("📊 Creating sample evaluation data...")
    samples = create_sample_evaluation_data()
    print(f"✅ Created {len(samples)} evaluation samples")
    
    # Initialize evaluator
    print("\n🤖 Initializing RAG evaluator...")
    try:
        groq_client = get_groq_client()
        evaluator = RAGEvaluator(groq_client=groq_client)
        print("✅ RAG evaluator initialized successfully")
    except Exception as e:
        print(f"❌ Error initializing evaluator: {e}")
        return
    
    # Run evaluation
    print("\n🔍 Running comprehensive evaluation...")
    try:
        results = evaluator.comprehensive_evaluation(samples)
        print("✅ Evaluation completed successfully!")
        
        # Display results
        print("\n📈 EVALUATION RESULTS")
        print("=" * 30)
        print(f"Overall Score: {results.overall_score:.3f}")
        print(f"Timestamp: {results.timestamp}")
        
        print("\n🔍 Retrieval Metrics:")
        for k, score in results.retrieval_metrics.f1_at_k.items():
            print(f"  F1@{k}: {score:.3f}")
        print(f"  MRR: {results.retrieval_metrics.mrr:.3f}")
        
        print("\n📊 Additional Metrics:")
        print(f"  BLEU Score: {results.answer_correctness_metrics.bleu_score:.3f}")
        print(f"  Semantic Similarity: {results.answer_correctness_metrics.semantic_similarity:.3f}")
        
        print("\n✅ Answer Correctness Metrics:")
        print(f"  LLM Judge: {results.answer_correctness_metrics.llm_judge_score:.3f}")
        print(f"  BLEU: {results.answer_correctness_metrics.bleu_score:.3f}")
        print(f"  ROUGE-1: {results.answer_correctness_metrics.rouge_scores['rouge1']:.3f}")
        print(f"  ROUGE-2: {results.answer_correctness_metrics.rouge_scores['rouge2']:.3f}")
        print(f"  ROUGE-L: {results.answer_correctness_metrics.rouge_scores['rougeL']:.3f}")
        
        # Save results
        results_file = "test_evaluation_results.json"
        evaluator.save_results(results, results_file)
        print(f"\n💾 Results saved to {results_file}")
        
        print("\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_evaluation()
