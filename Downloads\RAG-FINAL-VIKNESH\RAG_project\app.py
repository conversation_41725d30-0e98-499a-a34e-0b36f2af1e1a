"""
Clinical Trial RAG Application
A comprehensive RAG system for processing and querying clinical trial documents
"""

import streamlit as st
import uuid
import time
from typing import List, Dict, Optional

# Import custom modules
from document_processor import DocumentProcessor, validate_pdf_file
from embedding_generator import get_embedding_generator
from vector_database import get_vector_database
from groq_integration import get_groq_client
from prompt_suggestions import display_suggested_prompts


# Load custom CSS
def load_css():
    """Load custom CSS styling"""
    try:
        with open('styles.css', 'r') as f:
            css = f.read()
        st.markdown(f'<style>{css}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("Custom CSS file not found. Using default styling.")






def initialize_session_state():
    """Initialize session state variables"""
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    if 'current_document_id' not in st.session_state:
        st.session_state.current_document_id = None
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []

    if 'theme' not in st.session_state:
        st.session_state.theme = 'light'


def toggle_theme():
    """Toggle between light and dark themes"""
    if st.session_state.theme == 'light':
        st.session_state.theme = 'dark'
    else:
        st.session_state.theme = 'light'


def display_header():
    """Display the professional header with logo and branding"""
    # Add CSS to remove any remaining top spacing
    st.markdown("""
    <style>
    .main > div:first-child {
        margin-top: -2rem !important;
        padding-top: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Theme toggle in header - restored original proportions
    col1, col2 = st.columns([8, 1])

    with col1:
        st.markdown(f"""
        <div class="main-header-container" data-theme="{st.session_state.theme}">
            <div class="header-content">
                <div class="logo-container">
                    <div class="clinical-logo">🏥</div>
                </div>
                <div class="header-text">
                    <h1 class="main-title">Clinical Trial Research Report</h1>
                    <p class="subtitle">all-MiniLM-L6-v2 (HF) • Qdrant (HNSW) • Few-shot Retrieval-Augmented • Paragraph</p>
                    <p class="tagline">Advanced RAG-based Clinical Trial Research Report Generator</p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        # Theme toggle button
        theme_icon = "🌙" if st.session_state.theme == 'light' else "☀️"
        theme_text = "Dark" if st.session_state.theme == 'light' else "Light"

        if st.button(f"{theme_icon} {theme_text}", key="theme_toggle", help="Toggle theme"):
            toggle_theme()
            st.rerun()


def upload_and_process_documents():
    """Handle batch document upload and processing"""
    st.subheader("📋 Batch Document Upload & Processing")

    # Multi-file uploader for batch processing
    uploaded_files = st.file_uploader(
        "Upload up to 10 clinical trial PDF documents",
        type=['pdf'],
        accept_multiple_files=True,
        help="Upload multiple PDF documents containing clinical trial information (max 10 files)"
    )

    if uploaded_files:
        # Limit to 10 files
        if len(uploaded_files) > 10:
            st.warning("⚠️ Maximum 10 files allowed. Only the first 10 files will be processed.")
            uploaded_files = uploaded_files[:10]

        # Display uploaded files info
        st.success(f"✅ {len(uploaded_files)} file(s) uploaded")

        # Show file details in expandable section
        with st.expander(f"📋 View uploaded files ({len(uploaded_files)})"):
            total_size = 0
            valid_files = []

            for i, file in enumerate(uploaded_files):
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    st.write(f"📄 {file.name}")

                with col2:
                    file_size_kb = file.size / 1024
                    st.write(f"{file_size_kb:.1f} KB")
                    total_size += file_size_kb

                with col3:
                    if validate_pdf_file(file):
                        st.write("✅ Valid")
                        valid_files.append(file)
                    else:
                        st.write("❌ Invalid")

            st.info(f"📊 Total size: {total_size:.1f} KB | Valid files: {len(valid_files)}/{len(uploaded_files)}")

        # Process documents button
        if valid_files and st.button("🔄 Process All Documents", type="primary"):
            process_multiple_documents(valid_files)


def process_multiple_documents(uploaded_files):
    """Process multiple uploaded documents in batch"""
    try:
        st.subheader("🔄 Batch Processing Progress")

        # Initialize processors
        doc_processor = DocumentProcessor()
        embedding_gen = get_embedding_generator()
        vector_db = get_vector_database()

        # Overall progress tracking
        total_files = len(uploaded_files)
        overall_progress = st.progress(0)
        status_text = st.empty()

        # Individual file progress containers
        file_progress_containers = []
        for i, file in enumerate(uploaded_files):
            container = st.container()
            with container:
                st.write(f"📄 **{file.name}**")
                progress_bar = st.progress(0)
                status = st.empty()
            file_progress_containers.append((progress_bar, status))

        # Process each document
        processed_count = 0
        batch_results = []

        for i, uploaded_file in enumerate(uploaded_files):
            try:
                progress_bar, status = file_progress_containers[i]

                # Update overall status
                status_text.text(f"Processing file {i+1}/{total_files}: {uploaded_file.name}")

                # Extract text
                status.text("📖 Extracting text from PDF...")
                document = doc_processor.process_document(uploaded_file, uploaded_file.name)
                progress_bar.progress(25)

                # Generate embeddings
                status.text("🧠 Generating embeddings...")
                chunks_with_embeddings = embedding_gen.generate_chunk_embeddings(document['chunks'])
                progress_bar.progress(50)

                # Store in vector database with enhanced metadata
                status.text("🗄️ Storing in vector database...")
                document_id = str(uuid.uuid4())

                # Prepare enhanced metadata
                enhanced_metadata = {
                    'upload_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'file_size': uploaded_file.size,
                    'total_text_length': document['total_text_length'],
                    'num_chunks': document['num_chunks']
                }

                vector_db.store_document_chunks(
                    chunks_with_embeddings,
                    document_id,
                    uploaded_file.name,
                    enhanced_metadata
                )
                progress_bar.progress(75)

                # Store in session state
                st.session_state.processed_documents[document_id] = {
                    'filename': uploaded_file.name,
                    'chunks': chunks_with_embeddings,
                    'metadata': {
                        'total_text_length': document['total_text_length'],
                        'num_chunks': document['num_chunks'],
                        'processed_at': time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }

                progress_bar.progress(100)
                status.text("✅ Completed successfully!")

                batch_results.append({
                    'filename': uploaded_file.name,
                    'document_id': document_id,
                    'success': True,
                    'metadata': document
                })

                processed_count += 1

            except Exception as e:
                progress_bar.progress(100)
                status.text(f"❌ Error: {str(e)[:50]}...")
                batch_results.append({
                    'filename': uploaded_file.name,
                    'success': False,
                    'error': str(e)
                })

            # Update overall progress
            overall_progress.progress((i + 1) / total_files)

        # Final status
        status_text.text(f"✅ Batch processing complete! {processed_count}/{total_files} files processed successfully.")

        if processed_count > 0:
            st.success(f"🎉 Successfully processed {processed_count} out of {total_files} documents!")
            display_batch_processing_results(batch_results)

    except Exception as e:
        st.error(f"❌ Error in batch processing: {str(e)}")


def process_document(uploaded_file):
    """Process a single uploaded document (legacy function for compatibility)"""
    process_multiple_documents([uploaded_file])


def display_batch_processing_results(batch_results):
    """Display batch processing results"""
    st.subheader("📊 Batch Processing Results")

    # Summary metrics
    successful_files = [r for r in batch_results if r['success']]
    failed_files = [r for r in batch_results if not r['success']]

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Files", len(batch_results))

    with col2:
        st.metric("Successful", len(successful_files))

    with col3:
        st.metric("Failed", len(failed_files))

    with col4:
        total_chunks = sum(r['metadata']['num_chunks'] for r in successful_files if 'metadata' in r)
        st.metric("Total Chunks", total_chunks)

    # Detailed results
    if successful_files:
        st.subheader("✅ Successfully Processed Documents")
        for result in successful_files:
            with st.expander(f"📄 {result['filename']}"):
                if 'metadata' in result:
                    metadata = result['metadata']
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.write(f"**Text Length:** {metadata['total_text_length']:,} chars")
                    with col2:
                        st.write(f"**Chunks:** {metadata['num_chunks']}")
                    with col3:
                        avg_length = metadata['total_text_length'] // metadata['num_chunks'] if metadata['num_chunks'] > 0 else 0
                        st.write(f"**Avg Chunk:** {avg_length} chars")

    if failed_files:
        st.subheader("❌ Failed Documents")
        for result in failed_files:
            with st.expander(f"📄 {result['filename']} - Error"):
                st.error(f"Error: {result.get('error', 'Unknown error')}")


def display_processing_results(document):
    """Display single document processing results (legacy function)"""
    display_batch_processing_results([{
        'filename': 'Document',
        'success': True,
        'metadata': document
    }])


def query_interface():
    """Handle user queries and cross-document search with smart suggestions"""
    st.header("🔍 Cross-Document Query Interface")

    if not st.session_state.processed_documents:
        st.warning("⚠️ Please upload and process documents first.")
        st.info("👆 Go to **Document Upload** section to add your clinical trial PDFs")
        return

    # Display document corpus info
    total_docs = len(st.session_state.processed_documents)
    total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())

    st.success(f"📚 Knowledge Base Ready: {total_docs} documents with {total_chunks} total chunks available for search")

    # Initialize query from session state
    if 'current_query' not in st.session_state:
        st.session_state.current_query = ""

    # Query input
    query = st.text_area(
        "Enter your question about the clinical trials:",
        value=st.session_state.current_query,
        placeholder="e.g., What were the primary endpoints across these studies?",
        help="Ask questions that will search across all uploaded documents simultaneously",
        height=100,
        key="query_input"
    )

    # Update session state
    st.session_state.current_query = query

    # Smart suggested prompts section
    st.markdown("---")
    display_suggested_prompts(st.session_state.processed_documents)

    # Advanced settings
    with st.expander("🔧 Advanced Settings"):
        col1, col2 = st.columns(2)
        with col1:
            top_k = st.slider("Number of relevant chunks to retrieve", 1, 20, 8)
            temperature = st.slider("Response creativity (temperature)", 0.0, 1.0, 0.3, 0.1)
            score_threshold = st.slider(
                "Similarity threshold",
                0.0, 1.0, 0.0, 0.05,
                help="Lower values return more results. Set to 0.0 to get all results ranked by similarity."
            )
        with col2:
            max_tokens = st.slider("Maximum response length", 256, 2048, 1024, 128)

            # Document filtering option
            st.write("**Document Filtering (Optional)**")
            available_docs = list(st.session_state.processed_documents.items())
            doc_names = [f"{doc_data['filename']}" for doc_id, doc_data in available_docs]

            selected_docs = st.multiselect(
                "Filter by specific documents:",
                options=range(len(available_docs)),
                format_func=lambda x: doc_names[x],
                help="Leave empty to search all documents"
            )

        # Add a warning about similarity threshold
        if score_threshold > 0.1:
            st.warning(f"⚠️ Similarity threshold is set to {score_threshold:.2f}. This might filter out relevant results. Try lowering it to 0.0 for better results.")

    # Process query
    col1, col2, col3 = st.columns([2, 1, 1])
    with col1:
        search_button = st.button("🚀 Search All Documents", type="primary", use_container_width=True)
    with col2:
        clear_button = st.button("🗑️ Clear", type="secondary", use_container_width=True)
    with col3:
        if score_threshold > 0.0:
            if st.button("🔧 Fix Threshold", type="secondary", use_container_width=True, help="Reset similarity threshold to 0.0"):
                st.rerun()

    if clear_button:
        st.session_state.current_query = ""
        st.rerun()

    if search_button and query.strip():
        # Get selected document IDs for filtering
        document_filter = None
        if selected_docs:
            document_filter = [available_docs[i][0] for i in selected_docs]

        process_cross_document_query(query, top_k, temperature, max_tokens, score_threshold, document_filter)
    elif search_button and not query.strip():
        st.error("⚠️ Please enter a question before searching.")


def process_cross_document_query(query: str, top_k: int, temperature: float, max_tokens: int,
                               score_threshold: float, document_filter: List[str] = None):
    """Process cross-document query and generate response"""
    try:
        with st.spinner("Searching across all documents..."):
            # Initialize components
            embedding_gen = get_embedding_generator()
            vector_db = get_vector_database()
            groq_client = get_groq_client()

            # Generate query embedding
            query_embedding = embedding_gen.generate_query_embedding(query)

            # Search for similar chunks across all documents
            similar_chunks = vector_db.search_similar_chunks(
                query_embedding.tolist(),
                top_k=top_k,
                score_threshold=score_threshold,
                document_filter=document_filter
            )

            if not similar_chunks:
                st.error("❌ No relevant information found for your query.")
                st.info(f"""
                **Troubleshooting Tips:**
                - Try lowering the similarity threshold (currently: {score_threshold:.2f})
                - Use different keywords or rephrase your question
                - Check if your documents were processed correctly
                - Try a broader question first
                """)

                # Show debug info
                with st.expander("🔍 Debug Information"):
                    st.write(f"**Query:** {query}")
                    st.write(f"**Similarity threshold:** {score_threshold}")
                    st.write(f"**Top K:** {top_k}")
                    st.write(f"**Documents to search:** {len(st.session_state.processed_documents)}")
                    if document_filter:
                        st.write(f"**Filtered documents:** {len(document_filter)}")
                return

            # Display search summary
            unique_docs = set(chunk['filename'] for chunk in similar_chunks)
            st.success(f"🔍 Found {len(similar_chunks)} relevant chunks from {len(unique_docs)} document(s)")

            # Display retrieved chunks with source attribution
            st.subheader("📚 Retrieved Relevant Information")

            # Group chunks by document for better organization
            chunks_by_doc = {}
            for chunk in similar_chunks:
                doc_name = chunk['filename']
                if doc_name not in chunks_by_doc:
                    chunks_by_doc[doc_name] = []
                chunks_by_doc[doc_name].append(chunk)

            # Display chunks organized by document
            for doc_name, doc_chunks in chunks_by_doc.items():
                st.write(f"**📄 From: {doc_name}** ({len(doc_chunks)} chunks)")

                for i, chunk in enumerate(doc_chunks):
                    chunk_index = similar_chunks.index(chunk) + 1
                    with st.expander(f"Chunk {chunk_index} - Similarity: {chunk['score']:.3f}"):
                        st.markdown(f'<div class="chunk-display">{chunk["text"]}</div>', unsafe_allow_html=True)

                        # Enhanced metadata display
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.caption(f"📄 **Source:** {chunk['filename']}")
                        with col2:
                            st.caption(f"📏 **Length:** {chunk['length']} chars")
                        with col3:
                            st.caption(f"🎯 **Score:** {chunk['score']:.3f}")

                st.markdown("---")

            # Generate response
            st.subheader("🤖 AI Response")
            with st.spinner("Generating cross-document response..."):
                response = groq_client.generate_response(
                    query,
                    similar_chunks,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                st.markdown(f"**Query:** {query}")
                st.markdown("**Answer:**")
                st.markdown(response)

                # Enhanced query history with cross-document info
                st.session_state.query_history.append({
                    'query': query,
                    'response': response,
                    'chunks_used': len(similar_chunks),
                    'documents_searched': len(unique_docs),
                    'source_documents': list(unique_docs),
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'cross_document': True
                })

    except Exception as e:
        st.error(f"❌ Error processing cross-document query: {str(e)}")


def process_query(query: str, top_k: int, temperature: float, max_tokens: int, score_threshold: float):
    """Legacy single document query processing (redirects to cross-document search)"""
    process_cross_document_query(query, top_k, temperature, max_tokens, score_threshold)


def sidebar_info():
    """Display enhanced sidebar information for cross-document system"""
    st.header("📋 Knowledge Base Status")

    # Document corpus overview
    st.subheader("📚 Document Corpus")
    if st.session_state.processed_documents:
        total_docs = len(st.session_state.processed_documents)
        total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())
        total_chars = sum(doc['metadata']['total_text_length'] for doc in st.session_state.processed_documents.values())

        # Summary metrics
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Documents", total_docs)
            st.metric("Total Chunks", total_chunks)
        with col2:
            st.metric("Avg Chunks/Doc", f"{total_chunks//total_docs if total_docs > 0 else 0}")
            st.metric("Total Text", f"{total_chars//1000}K chars")

        # Individual document details
        with st.expander("📄 Document Details"):
            for doc_id, doc_data in st.session_state.processed_documents.items():
                st.write(f"**{doc_data['filename']}**")
                st.caption(f"  • Chunks: {doc_data['metadata']['num_chunks']}")
                st.caption(f"  • Length: {doc_data['metadata']['total_text_length']:,} chars")
                st.caption(f"  • Processed: {doc_data['metadata']['processed_at']}")
                st.markdown("---")
    else:
        st.write("No documents in knowledge base yet")
        st.info("Upload documents to build your knowledge base")

    st.markdown("---")

    # Enhanced query history
    st.subheader("🔍 Recent Queries")
    if st.session_state.query_history:
        for i, query_data in enumerate(reversed(st.session_state.query_history[-3:])):
            query_num = len(st.session_state.query_history) - i
            with st.expander(f"Query {query_num}"):
                st.write(f"**Q:** {query_data['query'][:50]}...")
                st.caption(f"📝 {query_data.get('chunks_used', 0)} chunks used")
                st.caption(f"⏰ {query_data['timestamp']}")
    else:
        st.write("No queries yet")


def display_analytics():
    """Display analytics and insights about the knowledge base"""
    st.header("📊 Knowledge Base Analytics")

    if not st.session_state.processed_documents:
        st.warning("⚠️ No documents uploaded yet. Upload documents to see analytics.")
        return

    # Overall statistics
    total_docs = len(st.session_state.processed_documents)
    total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())
    total_chars = sum(doc['metadata']['total_text_length'] for doc in st.session_state.processed_documents.values())

    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Documents", total_docs)
    with col2:
        st.metric("Total Chunks", total_chunks)
    with col3:
        st.metric("Total Characters", f"{total_chars:,}")
    with col4:
        st.metric("Avg Chunk Size", f"{total_chars//total_chunks if total_chunks > 0 else 0}")

    st.markdown("---")

    # Document breakdown
    st.subheader("📄 Document Breakdown")
    doc_data = []
    for doc_id, doc_info in st.session_state.processed_documents.items():
        doc_data.append({
            "Document": doc_info['filename'],
            "Chunks": doc_info['metadata']['num_chunks'],
            "Characters": doc_info['metadata']['total_text_length'],
            "Processed": doc_info['metadata']['processed_at']
        })

    if doc_data:
        st.dataframe(doc_data, use_container_width=True)

    # Query statistics
    if st.session_state.query_history:
        st.markdown("---")
        st.subheader("🔍 Query Statistics")

        col1, col2 = st.columns(2)
        with col1:
            st.metric("Total Queries", len(st.session_state.query_history))
        with col2:
            avg_chunks = sum(q.get('chunks_used', 0) for q in st.session_state.query_history) / len(st.session_state.query_history)
            st.metric("Avg Chunks per Query", f"{avg_chunks:.1f}")

        # Recent queries
        st.subheader("📝 Recent Queries")
        for i, query_item in enumerate(reversed(st.session_state.query_history[-5:])):
            with st.expander(f"Query {len(st.session_state.query_history) - i}: {query_item['query'][:50]}..."):
                st.write(f"**Query:** {query_item['query']}")
                st.write(f"**Response:** {query_item['response'][:200]}...")
                st.write(f"**Chunks Used:** {query_item.get('chunks_used', 'N/A')}")
                st.write(f"**Documents Searched:** {query_item.get('documents_searched', 'N/A')}")
                st.write(f"**Timestamp:** {query_item.get('timestamp', 'N/A')}")


def display_settings():
    """Display system settings and configuration"""
    st.header("⚙️ System Settings")

    st.subheader("🔧 System Configuration")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **Current Configuration:**
        - **Search Mode:** Cross-Document
        - **Embedding Model:** all-MiniLM-L6-v2
        - **LLM Model:** Llama 4 Scout 17B (Latest)
        - **Vector DB:** Qdrant (Local)
        """)

    with col2:
        st.info("""
        **Processing Settings:**
        - **Chunking:** Paragraph-based
        - **Max Documents:** 10 PDFs
        - **File Size Limit:** 200MB per file
        - **Supported Formats:** PDF only
        """)

    st.markdown("---")

    st.subheader("🗑️ Data Management")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("🗑️ Clear All Documents", type="secondary"):
            if st.session_state.processed_documents:
                st.session_state.processed_documents = {}
                st.success("✅ All documents cleared from knowledge base")
                st.rerun()
            else:
                st.info("No documents to clear")

    with col2:
        if st.button("🗑️ Clear Query History", type="secondary"):
            if st.session_state.query_history:
                st.session_state.query_history = []
                st.success("✅ Query history cleared")
                st.rerun()
            else:
                st.info("No query history to clear")


def main():
    """Main application function for cross-document RAG system"""
    # Configure page
    st.set_page_config(
        page_title="Clinical Trial RAG System",
        page_icon="🏥",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Initialize session state first
    initialize_session_state()
    
    # Initialize Groq API client on app start
    try:
        groq_client = get_groq_client()
        # Verify the client is properly configured with the correct model
        if not hasattr(st.session_state, 'groq_initialized'):
            # Store client reference in session state for easy access
            st.session_state.groq_client = groq_client
            st.session_state.groq_initialized = True
            
            # Verify model configuration
            expected_model = "meta-llama/llama-4-scout-17b-16e-instruct"
            if groq_client.model_name == expected_model:
                # Show success message only once during initialization
                if 'initialization_message_shown' not in st.session_state:
                    st.success(f"✅ Groq API client initialized successfully with model: {expected_model}")
                    st.session_state.initialization_message_shown = True
            else:
                st.warning(f"⚠️ Model mismatch: Expected {expected_model}, got {groq_client.model_name}")
    except Exception as e:
        st.error(f"❌ Failed to initialize Groq API client: {str(e)}")
        st.info("Please check your GROQ_API_KEY environment variable and internet connection.")

    # Load custom CSS with theme
    load_css()

    # Inject aggressive CSS to remove top spacing
    st.markdown("""
    <style>
    .main .block-container {
        padding-top: 0rem !important;
        margin-top: 0rem !important;
    }
    .stApp > header {
        display: none !important;
    }
    .stApp {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Apply theme to the app
    st.markdown(f'<div data-theme="{st.session_state.theme}" class="app-container">', unsafe_allow_html=True)

    # Navigation sidebar
    with st.sidebar:
        st.markdown("## 🩺 Navigation")

        # Navigation options
        page = st.selectbox(
            "Choose a section:",
            ["📋 Document Upload", "🔬 Query Interface", "� RAG Evaluation", "�📈 Analytics", "⚙️ Settings"],
            index=0
        )

        st.markdown("---")

        # Enhanced sidebar information
        sidebar_info()

    # Display professional header
    display_header()

    # Main content area based on navigation
    if page == "📋 Document Upload":
        upload_and_process_documents()

    elif page == "🔬 Query Interface":
        query_interface()

    elif page == "📋 Research Report":
        from clinical_report_generator import display_report_generator
        display_report_generator()

    elif page == "� RAG Evaluation":
        from simplified_evaluation import display_simplified_evaluation_dashboard
        display_simplified_evaluation_dashboard()

    elif page == "�📈 Analytics":
        display_analytics()

    elif page == "⚙️ Settings":
        display_settings()

    # Fallback for pages (handle encoding issues)
    elif "RAG Evaluation" in page or "Evaluation" in page:
        from simplified_evaluation import display_simplified_evaluation_dashboard
        display_simplified_evaluation_dashboard()

    elif "Research Report" in page or "Report" in page:
        from clinical_report_generator import display_report_generator
        display_report_generator()

    # Close theme container
    st.markdown('</div>', unsafe_allow_html=True)


if __name__ == "__main__":
    main()