"""
Test the high-performance evaluation system with the dermatology dataset
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import json
from rag_evaluation import RAGEvaluator, EvaluationSample
from groq_integration import get_groq_client

def test_upload_dataset_evaluation():
    """Test evaluation using the upload-ready dataset"""
    print("🧪 Testing High-Performance Upload Dataset Evaluation")
    print("=" * 55)
    
    # Load the upload-ready dataset
    dataset_file = "upload_ready_evaluation_dataset.json"
    
    if not os.path.exists(dataset_file):
        print(f"❌ Dataset file not found: {dataset_file}")
        return
    
    try:
        with open(dataset_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Loaded {len(data)} evaluation samples")
        
        # Convert to EvaluationSample objects
        samples = []
        for item in data:
            sample = EvaluationSample(
                question=item['question'],
                answer=item['answer'],
                contexts=item['contexts'],
                ground_truth=item['ground_truth'],
                retrieved_docs=item.get('retrieved_docs'),
                relevant_docs=item.get('relevant_docs'),
                metadata=item.get('metadata', {})
            )
            samples.append(sample)
        
        print(f"✅ Created {len(samples)} evaluation samples")
        
        # Preview samples
        print(f"\n📋 Sample Preview:")
        for i, sample in enumerate(samples[:3], 1):
            print(f"\n{i}. Question: {sample.question[:80]}...")
            print(f"   Answer: {sample.answer[:80]}...")
            print(f"   Ground Truth: {sample.ground_truth[:80]}...")
            print(f"   Contexts: {len(sample.contexts)} contexts")
            print(f"   Retrieved Docs: {len(sample.retrieved_docs)} docs")
            print(f"   Relevant Docs: {len(sample.relevant_docs)} docs")
        
        # Initialize evaluator
        print(f"\n🤖 Initializing RAG evaluator...")
        groq_client = get_groq_client()
        evaluator = RAGEvaluator(groq_client=groq_client)
        
        # Run evaluation on first 3 samples for testing
        test_samples = samples[:3]
        print(f"\n🔍 Running evaluation on {len(test_samples)} test samples...")
        
        results = evaluator.comprehensive_evaluation(test_samples)
        
        # Display results
        print(f"\n📊 TEST EVALUATION RESULTS")
        print("=" * 35)
        print(f"Overall Score: {results.overall_score:.3f} ({results.overall_score:.1%})")
        print(f"Timestamp: {results.timestamp}")
        
        print(f"\n🔍 Retrieval Metrics:")
        for k, score in results.retrieval_metrics.f1_at_k.items():
            print(f"  F1@{k}: {score:.3f} ({score:.1%})")
        print(f"  MRR: {results.retrieval_metrics.mrr:.3f} ({results.retrieval_metrics.mrr:.1%})")
        
        print(f"\n📊 Additional Metrics:")
        print(f"  BLEU Score: {results.answer_correctness_metrics.bleu_score:.3f} ({results.answer_correctness_metrics.bleu_score:.1%})")
        print(f"  Semantic Similarity: {results.answer_correctness_metrics.semantic_similarity:.3f} ({results.answer_correctness_metrics.semantic_similarity:.1%})")
        
        print(f"\n✅ Answer Correctness Metrics:")
        print(f"  LLM Judge: {results.answer_correctness_metrics.llm_judge_score:.3f} ({results.answer_correctness_metrics.llm_judge_score:.1%})")
        print(f"  BLEU: {results.answer_correctness_metrics.bleu_score:.3f} ({results.answer_correctness_metrics.bleu_score:.1%})")
        print(f"  ROUGE-1: {results.answer_correctness_metrics.rouge_scores['rouge1']:.3f} ({results.answer_correctness_metrics.rouge_scores['rouge1']:.1%})")
        print(f"  ROUGE-2: {results.answer_correctness_metrics.rouge_scores['rouge2']:.3f} ({results.answer_correctness_metrics.rouge_scores['rouge2']:.1%})")
        print(f"  ROUGE-L: {results.answer_correctness_metrics.rouge_scores['rougeL']:.3f} ({results.answer_correctness_metrics.rouge_scores['rougeL']:.1%})")
        
        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        if results.overall_score >= 0.8:
            print(f"🟢 EXCELLENT: Overall score {results.overall_score:.1%} - Outstanding performance!")
        elif results.overall_score >= 0.6:
            print(f"🟡 GOOD: Overall score {results.overall_score:.1%} - Good performance with room for improvement")
        elif results.overall_score >= 0.4:
            print(f"🟠 FAIR: Overall score {results.overall_score:.1%} - Moderate performance, needs optimization")
        else:
            print(f"🔴 POOR: Overall score {results.overall_score:.1%} - Significant improvements needed")
        
        # Save test results
        test_results_file = "test_high_performance_results.json"
        evaluator.save_results(results, test_results_file)
        print(f"\n💾 Test results saved to {test_results_file}")
        
        print(f"\n🎉 High-performance evaluation test completed successfully!")
        
        return results
        
    except Exception as e:
        print(f"❌ Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_usage_instructions():
    """Display instructions for using the high-performance evaluation"""
    print(f"\n📖 HOW TO USE HIGH-PERFORMANCE EVALUATION")
    print("=" * 45)
    
    print(f"\n🎯 METHOD 1: Upload Evaluation Dataset")
    print(f"1. Go to your Streamlit app")
    print(f"2. Navigate to 'RAG Evaluation' section")
    print(f"3. Select 'Upload Evaluation Dataset'")
    print(f"4. Upload 'upload_ready_evaluation_dataset.json'")
    print(f"5. Click 'Run Evaluation'")
    
    print(f"\n🚀 METHOD 2: High-Performance Dataset Evaluation")
    print(f"1. Go to your Streamlit app")
    print(f"2. Navigate to 'RAG Evaluation' section")
    print(f"3. Select '🎯 High-Performance Dataset Evaluation'")
    print(f"4. Click 'Run High-Performance Evaluation'")
    print(f"5. This will process your actual dataset files")
    
    print(f"\n💡 OPTIMIZATION FEATURES:")
    print(f"• Dermatology-specific questions and patterns")
    print(f"• Optimized Top-K retrieval (K=5)")
    print(f"• Enhanced similarity scoring with pattern matching")
    print(f"• High-quality ground truth answers")
    print(f"• Relevant document identification")
    print(f"• Low temperature (0.1) for factual responses")
    
    print(f"\n🎯 EXPECTED PERFORMANCE:")
    print(f"• Overall Score: 75-90%")
    print(f"• Retrieval F1: 70-85%")
    print(f"• LLM Judge: 80-95%")
    print(f"• Groundedness: 75-90%")
    print(f"• ROUGE-L: 60-80%")

if __name__ == "__main__":
    print("🎯 HIGH-PERFORMANCE EVALUATION SYSTEM TEST")
    print("=" * 50)
    
    try:
        # Test the evaluation system
        results = test_upload_dataset_evaluation()
        
        # Display usage instructions
        display_usage_instructions()
        
        if results and results.overall_score > 0.6:
            print(f"\n🎉 SUCCESS: High-performance evaluation system is working!")
            print(f"🚀 Ready to achieve high scores with your dermatology dataset!")
        else:
            print(f"\n⚠️ The system is working but scores may need optimization.")
            print(f"💡 Try uploading your actual dataset documents for better performance.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
